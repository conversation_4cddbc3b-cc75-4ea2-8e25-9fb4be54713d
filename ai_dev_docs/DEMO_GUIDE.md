# Shadcn Admin Dashboard 国际化功能演示指南

## 🎯 演示目标

本指南将帮助您快速体验和测试 Shadcn Admin Dashboard 项目的完整国际化功能。

## 🚀 快速开始

### 1. 启动项目
```bash
npm run dev
```

### 2. 打开浏览器
访问 http://localhost:5173/

## 🔍 功能演示步骤

### 步骤 1: 查看默认语言
- 项目默认使用中文简体
- 观察侧边栏菜单、页面标题都是中文显示

### 步骤 2: 找到语言切换器
- 在页面右上角，ThemeSwitch（主题切换）和 ProfileDropdown（用户菜单）之间
- 图标是一个地球符号 🌐

### 步骤 3: 切换到英文
- 点击语言切换器
- 选择 "English" 选项
- 观察页面内容立即切换为英文

### 步骤 4: 测试各个页面
访问以下页面验证国际化效果：

#### Dashboard 页面 (/)
- 页面标题: "仪表板" ↔ "Dashboard"
- 卡片标题: "总收入" ↔ "Total Revenue"
- 按钮文字: "下载" ↔ "Download"

#### Tasks 页面 (/tasks)
- 页面标题: "任务" ↔ "Tasks"
- 描述文字: "这是您本月的任务列表！" ↔ "Here's a list of your tasks for this month!"

#### Users 页面 (/users)
- 页面标题: "用户" ↔ "Users"
- 描述文字: "管理您的团队成员和他们的账户权限。" ↔ "Manage your team members and their account permissions here."

#### Apps 页面 (/apps)
- 页面标题: "应用集成" ↔ "App Integrations"
- 搜索框: "筛选应用..." ↔ "Filter apps..."
- 按钮文字: "连接" ↔ "Connect"

#### Settings 页面 (/settings)
- 页面标题: "设置" ↔ "Settings"
- 子菜单: "个人资料" ↔ "Profile", "账户" ↔ "Account"

#### Chats 页面 (/chats)
- 页面标题: "聊天" ↔ "Chats"
- 搜索框: "搜索聊天..." ↔ "Search chats..."
- 输入框: "输入消息..." ↔ "Type a message..."

### 步骤 5: 测试侧边栏导航
- 观察侧边栏所有菜单项的翻译
- "仪表板" ↔ "Dashboard"
- "任务" ↔ "Tasks"
- "应用" ↔ "Apps"
- "聊天" ↔ "Chats"
- "用户" ↔ "Users"
- "设置" ↔ "Settings"

### 步骤 6: 测试持久化
- 切换语言后刷新页面
- 验证语言设置被保存
- 重新打开浏览器标签页，语言设置依然保持

## 🎨 设计特点

### 视觉一致性
- 语言切换器与 ThemeSwitch 使用相同的设计风格
- 下拉菜单样式保持一致
- 图标和交互效果统一

### 用户体验
- 实时切换，无需刷新页面
- 清晰的当前语言指示（✓ 标记）
- 国旗 emoji 增强识别度

### 技术特性
- TypeScript 类型安全
- 响应式设计
- 键盘导航支持
- 屏幕阅读器友好

## 🧪 高级测试

### 测试翻译键
打开浏览器开发者工具，在控制台中运行：
```javascript
// 测试翻译函数
console.log(window.i18n.t('navigation.dashboard'))
console.log(window.i18n.t('buttons.save'))
```

### 测试语言检测
```javascript
// 查看当前语言
console.log(window.i18n.language)

// 手动切换语言
window.i18n.changeLanguage('en-US')
window.i18n.changeLanguage('zh-CN')
```

## 📝 反馈和问题

如果在测试过程中发现任何问题，请检查：

1. **翻译缺失**: 某些文字没有翻译
2. **样式问题**: 不同语言下的布局问题
3. **功能异常**: 语言切换不生效
4. **性能问题**: 切换语言时的延迟

## 🎉 总结

通过以上演示，您应该能够：
- ✅ 成功切换中英文界面
- ✅ 验证所有主要页面的国际化
- ✅ 确认语言设置的持久化
- ✅ 体验流畅的用户交互

这个国际化实现为项目提供了完整的多语言支持基础，可以轻松扩展到更多语言和更复杂的本地化需求。
