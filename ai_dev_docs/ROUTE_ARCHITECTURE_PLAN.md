# Shadcn Admin Dashboard 路由架构重构方案

## 🎯 设计目标

1. **明确分离**：营销页面 vs 仪表板页面
2. **SEO 友好**：营销页面优化搜索引擎
3. **用户体验**：流畅的导航和认证流程
4. **可维护性**：清晰的代码组织结构
5. **性能优化**：代码分割和懒加载

## 🏗️ 新的目录结构

```
src/routes/
├── __root.tsx                          # 根路由
├── index.tsx                           # Landing Page (/)
├── (marketing)/                        # 营销页面组
│   ├── pricing.tsx                     # /pricing
│   ├── blog/                           # /blog/*
│   │   ├── index.tsx                   # /blog
│   │   └── [slug].tsx                  # /blog/[slug]
│   ├── about.tsx                       # /about
│   ├── contact.tsx                     # /contact
│   └── features.tsx                    # /features
├── dashboard/                          # 仪表板页面组
│   ├── route.tsx                       # Dashboard 布局
│   ├── index.tsx                       # /dashboard (重定向到 overview)
│   ├── overview.tsx                    # /dashboard/overview
│   ├── tasks/                          # /dashboard/tasks/*
│   │   └── index.tsx
│   ├── users/                          # /dashboard/users/*
│   │   └── index.tsx
│   ├── apps/                           # /dashboard/apps/*
│   │   └── index.tsx
│   ├── chats/                          # /dashboard/chats/*
│   │   └── index.tsx
│   ├── settings/                       # /dashboard/settings/*
│   │   ├── route.tsx
│   │   ├── index.tsx
│   │   ├── account.tsx
│   │   ├── appearance.tsx
│   │   ├── notifications.tsx
│   │   └── display.tsx
│   └── help-center/                    # /dashboard/help-center/*
│       └── index.tsx
├── (auth)/                             # 认证页面组 (保持现有)
│   ├── sign-in.tsx
│   ├── sign-up.tsx
│   ├── sign-in-2.tsx
│   ├── forgot-password.tsx
│   └── otp.tsx
├── (errors)/                           # 错误页面组 (保持现有)
│   ├── 401.tsx
│   ├── 403.tsx
│   ├── 404.tsx
│   ├── 500.tsx
│   └── 503.tsx
└── clerk/                              # Clerk 相关路由 (保持现有)
    ├── route.tsx
    ├── (auth)/
    └── _authenticated/
```

## 🔄 URL 映射对比

### 当前 URL 结构
```
/                    -> Dashboard (需要认证)
/tasks              -> Tasks
/users              -> Users
/apps               -> Apps
/chats              -> Chats
/settings           -> Settings
/help-center        -> Help Center
/sign-in            -> Sign In
/sign-up            -> Sign Up
```

### 新的 URL 结构
```
# 营销页面 (无需认证)
/                    -> Landing Page
/pricing            -> Pricing Page
/blog               -> Blog Index
/blog/post-slug     -> Blog Post
/about              -> About Us
/contact            -> Contact
/features           -> Features

# 仪表板页面 (需要认证)
/dashboard          -> 重定向到 /dashboard/overview
/dashboard/overview -> Dashboard 主页
/dashboard/tasks    -> Tasks
/dashboard/users    -> Users
/dashboard/apps     -> Apps
/dashboard/chats    -> Chats
/dashboard/settings -> Settings
/dashboard/help-center -> Help Center

# 认证页面 (保持不变)
/sign-in            -> Sign In
/sign-up            -> Sign Up
/forgot-password    -> Forgot Password
```

## 🎨 布局组件设计

### 1. MarketingLayout
```typescript
interface MarketingLayoutProps {
  children: React.ReactNode
}

export function MarketingLayout({ children }: MarketingLayoutProps) {
  return (
    <div className="min-h-screen">
      <MarketingHeader />
      <main>{children}</main>
      <MarketingFooter />
    </div>
  )
}
```

### 2. DashboardLayout (重构 AuthenticatedLayout)
```typescript
interface DashboardLayoutProps {
  children: React.ReactNode
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  return (
    <SearchProvider>
      <SidebarProvider>
        <AppSidebar />
        <div className="main-content">
          <AppHeader />
          <main>{children}</main>
        </div>
      </SidebarProvider>
    </SearchProvider>
  )
}
```

### 3. AuthLayout (保持现有)
用于登录、注册等认证页面

## 🔐 认证和重定向策略

### 1. 路由守卫
```typescript
// 仪表板页面需要认证
export const Route = createFileRoute('/dashboard')({
  beforeLoad: ({ context }) => {
    if (!context.auth.isAuthenticated) {
      throw redirect({
        to: '/sign-in',
        search: { redirect: location.href }
      })
    }
  },
  component: DashboardLayout,
})
```

### 2. 重定向逻辑
- 未认证用户访问 `/dashboard/*` → 重定向到 `/sign-in?redirect=...`
- 已认证用户访问 `/sign-in` → 重定向到 `/dashboard/overview`
- 访问 `/dashboard` → 重定向到 `/dashboard/overview`
- 访问根路径 `/` → 显示 Landing Page (营销页面)

## 📱 导航系统设计

### 1. MarketingHeader
```typescript
export function MarketingHeader() {
  const { isAuthenticated } = useAuth()
  
  return (
    <header>
      <nav>
        <Logo />
        <NavLinks>
          <Link to="/features">Features</Link>
          <Link to="/pricing">Pricing</Link>
          <Link to="/blog">Blog</Link>
          <Link to="/about">About</Link>
        </NavLinks>
        <AuthActions>
          {isAuthenticated ? (
            <Link to="/dashboard">Dashboard</Link>
          ) : (
            <>
              <Link to="/sign-in">Sign In</Link>
              <Link to="/sign-up">Get Started</Link>
            </>
          )}
        </AuthActions>
      </nav>
    </header>
  )
}
```

### 2. DashboardSidebar (更新现有)
```typescript
// 更新侧边栏链接，添加 /dashboard 前缀
const sidebarLinks = [
  { title: 'Overview', href: '/dashboard/overview' },
  { title: 'Tasks', href: '/dashboard/tasks' },
  { title: 'Users', href: '/dashboard/users' },
  // ...
]
```

## 🚀 SEO 优化

### 1. 营销页面 SEO
```typescript
export const Route = createFileRoute('/')({
  component: LandingPage,
  meta: () => [
    { title: 'Shadcn Admin - Modern Dashboard Solution' },
    { name: 'description', content: 'Build beautiful admin dashboards with Shadcn UI components' },
    { name: 'keywords', content: 'admin, dashboard, react, shadcn, ui' },
  ],
})
```

### 2. 动态 Meta 标签
```typescript
export const Route = createFileRoute('/blog/$slug')({
  component: BlogPost,
  meta: ({ params }) => [
    { title: `${post.title} - Shadcn Admin Blog` },
    { name: 'description', content: post.excerpt },
  ],
})
```

## ⚡ 性能优化

### 1. 代码分割
```typescript
// 懒加载营销页面
const LandingPage = lazy(() => import('@/features/marketing/landing'))
const PricingPage = lazy(() => import('@/features/marketing/pricing'))

// 懒加载仪表板页面
const Dashboard = lazy(() => import('@/features/dashboard'))
const Tasks = lazy(() => import('@/features/tasks'))
```

### 2. 预加载策略
```typescript
export const Route = createFileRoute('/')({
  component: LandingPage,
  preload: false, // 营销页面不预加载仪表板代码
})

export const Route = createFileRoute('/dashboard')({
  component: DashboardLayout,
  preload: 'intent', // 仪表板页面预加载相关代码
})
```

## 🔄 迁移步骤

### 阶段 1: 创建新的路由结构
1. 创建营销页面路由和组件
2. 创建新的 dashboard 路由组
3. 创建 MarketingLayout 组件

### 阶段 2: 迁移现有页面
1. 将现有的 _authenticated 页面迁移到 dashboard 路由组
2. 更新所有内部链接，添加 /dashboard 前缀
3. 更新侧边栏导航配置

### 阶段 3: 实现认证逻辑
1. 添加路由守卫
2. 实现重定向逻辑
3. 更新认证状态管理

### 阶段 4: 优化和测试
1. 实现 SEO 优化
2. 添加代码分割
3. 性能测试和优化

这个方案提供了清晰的路由分离、良好的用户体验和强大的扩展性。
