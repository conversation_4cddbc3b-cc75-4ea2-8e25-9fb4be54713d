# 🚀 企业级统一 API 请求解决方案

## 📋 项目概述

基于 React 19 + TypeScript + TanStack Router + Shadcn UI 技术栈，使用 axios 和 @tanstack/react-query 封装的企业级统一 API 请求解决方案。

## ✨ 核心特性

### 🔒 安全防护
- ✅ **JWT 认证管理** - 自动添加、刷新和过期处理
- ✅ **XSS 防护** - 自动清理用户输入数据
- ✅ **CSRF 保护** - 自动添加 CSRF token
- ✅ **数据加密** - 支持敏感数据传输加密
- ✅ **请求签名** - 可选的请求签名验证

### 🎯 错误处理
- ✅ **统一错误处理** - HTTP 状态码、业务逻辑、网络错误
- ✅ **用户友好提示** - 使用 sonner toast 组件
- ✅ **国际化支持** - 错误消息本地化（中英文）
- ✅ **错误分类** - 网络、HTTP、业务错误的分类处理
- ✅ **错误监控** - 生产环境错误上报

### ⚡ 性能优化
- ✅ **请求去重** - 自动去除重复请求
- ✅ **智能缓存** - 基于时间的缓存策略
- ✅ **自动重试** - 网络错误和服务器错误重试
- ✅ **请求取消** - 路由切换时自动取消
- ✅ **并发控制** - 批量请求和并发限制

### 📁 文件处理
- ✅ **文件上传** - 支持进度显示、类型验证、大小限制
- ✅ **拖拽上传** - 支持拖拽的多文件上传
- ✅ **分片上传** - 大文件分片上传
- ✅ **断点续传** - 支持断点续传下载
- ✅ **文件下载** - 支持进度显示的文件下载

### 🔧 开发体验
- ✅ **TypeScript 类型安全** - 完整的类型定义
- ✅ **React Query 集成** - 强大的数据状态管理
- ✅ **便捷 Hooks** - 预定义的业务 Hooks
- ✅ **开发日志** - 详细的请求/响应日志
- ✅ **请求统计** - API 调用统计和监控

## 📁 文件结构

```
src/lib/api/
├── index.ts              # 主入口文件
├── client.ts             # axios 客户端配置
├── types.ts              # TypeScript 类型定义
├── config.ts             # 环境配置和常量
├── utils.ts              # 工具函数和缓存管理
├── interceptors.ts       # 请求/响应拦截器
├── error-handler.ts      # 统一错误处理
├── hooks/                # React Query Hooks
│   ├── index.ts
│   ├── use-api-query.ts      # 查询 Hook
│   ├── use-api-mutation.ts   # 变更 Hook
│   ├── use-api-infinite-query.ts # 无限查询 Hook
│   ├── use-upload.ts         # 文件上传 Hook
│   └── use-download.ts       # 文件下载 Hook
├── endpoints/            # API 端点定义
│   ├── index.ts
│   ├── auth.ts              # 认证相关 API
│   ├── users.ts             # 用户相关 API
│   ├── files.ts             # 文件相关 API
│   └── system.ts            # 系统相关 API
├── test.ts               # API 系统测试
└── README.md             # 详细文档
```

## 🛠️ 技术实现

### 核心依赖
- **axios** - HTTP 客户端
- **@tanstack/react-query** - 数据状态管理
- **@tanstack/react-router** - 路由管理
- **sonner** - 消息提示
- **react-i18next** - 国际化
- **zustand** - 状态管理（认证）

### 架构设计
1. **分层架构** - 客户端 → 拦截器 → 端点 → Hooks → 组件
2. **插件化设计** - 可扩展的拦截器和中间件
3. **类型安全** - 完整的 TypeScript 类型系统
4. **配置驱动** - 环境变量和配置文件驱动

## 📖 使用示例

### 基础 API 调用
```tsx
import { api } from '@/lib/api'

// GET 请求
const users = await api.get('/users', { page: 1 })

// POST 请求
const newUser = await api.post('/users', { name: 'John' })
```

### React Query Hooks
```tsx
import { useApiQuery, useApiMutation } from '@/lib/api'

function UserList() {
  const { data, isLoading } = useApiQuery({
    url: '/users',
    queryKey: ['users']
  })

  const createUser = useApiMutation(
    (data) => api.post('/users', data)
  )

  return (
    <div>
      {data?.data.map(user => (
        <div key={user.id}>{user.name}</div>
      ))}
    </div>
  )
}
```

### 文件上传
```tsx
import { useUpload } from '@/lib/api'

function FileUpload() {
  const { uploadFile, isUploading, progress } = useUpload({
    endpoint: '/files/upload',
    accept: ['image/*'],
    maxSize: 5 * 1024 * 1024
  })

  return (
    <div>
      <input 
        type="file" 
        onChange={(e) => uploadFile(e.target.files[0])} 
      />
      {isUploading && <progress value={progress} max={100} />}
    </div>
  )
}
```

### 便捷业务 Hooks
```tsx
import { useUsers, useCreateUser } from '@/lib/api'

function UserManagement() {
  const { data: users } = useUsers()
  const createUser = useCreateUser()

  return (
    <div>
      <button onClick={() => createUser.mutate({ name: 'New User' })}>
        Create User
      </button>
      {users?.data.items.map(user => (
        <div key={user.id}>{user.name}</div>
      ))}
    </div>
  )
}
```

## 🔧 配置说明

### 环境变量
```env
# API 基础配置
VITE_API_BASE_URL=http://localhost:3001/api
VITE_APP_VERSION=1.0.0

# 安全配置
VITE_ENCRYPTION_KEY=your-encryption-key
VITE_ENCRYPTION_IV=your-encryption-iv

# 功能开关
VITE_ENABLE_MOCK=false
```

### 初始化
```tsx
// src/main.tsx
import { initializeAPI } from '@/lib/api'

// 初始化 API 系统
await initializeAPI()
```

## 🎯 最佳实践

1. **类型安全** - 充分利用 TypeScript 类型系统
2. **错误处理** - 使用统一的错误处理机制
3. **缓存策略** - 合理设置 staleTime 和 cacheTime
4. **请求优化** - 利用去重、重试、取消等机制
5. **安全防护** - 启用 XSS 防护和 CSRF 保护
6. **性能监控** - 使用请求统计和错误监控

## 📊 功能对比

| 功能 | 传统方案 | 本解决方案 |
|------|----------|------------|
| 错误处理 | 手动处理 | ✅ 统一自动处理 |
| 认证管理 | 手动添加 token | ✅ 自动管理和刷新 |
| 请求去重 | 无 | ✅ 自动去重 |
| 缓存管理 | 手动实现 | ✅ 智能缓存 |
| 文件上传 | 基础功能 | ✅ 进度、验证、分片 |
| 类型安全 | 部分支持 | ✅ 完整类型系统 |
| 国际化 | 手动翻译 | ✅ 自动本地化 |
| 性能优化 | 手动优化 | ✅ 内置优化 |

## 🚀 部署和维护

### 生产环境配置
- 启用错误监控和上报
- 配置适当的缓存策略
- 设置合理的超时时间
- 启用请求签名验证

### 监控指标
- API 请求成功率
- 平均响应时间
- 错误率和错误类型
- 缓存命中率

### 扩展性
- 支持自定义拦截器
- 可配置的错误处理策略
- 插件化的功能扩展
- 灵活的端点定义

## 🎉 总结

这套企业级 API 解决方案提供了：

- **完整的功能覆盖** - 从基础请求到高级功能
- **优秀的开发体验** - TypeScript + React Query + 便捷 Hooks
- **企业级安全性** - 多层安全防护机制
- **高性能优化** - 内置多种性能优化策略
- **易于维护** - 清晰的架构和完善的文档

通过统一的接口和强大的功能，可以大大提升开发效率，降低维护成本，确保应用的稳定性和安全性。
