# AppHeader 导航组件重构指南

## 🎯 重构目标

将 AppHeader 中硬编码的 TopNav 组件重构为可配置的导航系统，支持：
- 多种导航组件类型（TopNav、Breadcrumb、TabNav、自定义组件）
- 类型安全的配置接口
- 灵活的组件渲染机制
- 易于扩展和维护

## 🏗️ 架构设计

### 1. 类型系统设计

```typescript
// 导航组件类型枚举
enum NavigationComponentType {
  NONE = 'none',           // 不显示导航
  TOP_NAV = 'topNav',      // 顶部导航
  BREADCRUMB = 'breadcrumb', // 面包屑导航
  TAB_NAV = 'tabNav',      // 标签导航
  CUSTOM = 'custom',       // 自定义组件
}

// 导航配置联合类型
type NavigationConfig = 
  | { type: NavigationComponentType.NONE }
  | { type: NavigationComponentType.TOP_NAV; props: TopNavProps }
  | { type: NavigationComponentType.BREADCRUMB; props: BreadcrumbProps }
  | { type: NavigationComponentType.TAB_NAV; props: TabNavProps }
  | { type: NavigationComponentType.CUSTOM; config: CustomNavigationConfig }
```

### 2. 组件渲染器

```typescript
// 根据配置渲染对应的导航组件
function renderNavigation(config: NavigationConfig): ReactNode {
  switch (config.type) {
    case NavigationComponentType.TOP_NAV:
      return <TopNav {...config.props} />
    case NavigationComponentType.BREADCRUMB:
      return <Breadcrumb {...config.props} />
    case NavigationComponentType.TAB_NAV:
      return <TabNav {...config.props} />
    case NavigationComponentType.CUSTOM:
      const { component: CustomComponent, props } = config.config
      return <CustomComponent {...props} />
    default:
      return null
  }
}
```

### 3. 配置工厂模式

```typescript
class NavigationConfigFactory {
  constructor(private t: TFunction) {}

  createDashboardTopNav(): NavigationConfig {
    return {
      type: NavigationComponentType.TOP_NAV,
      props: { links: [...] }
    }
  }

  createSettingsBreadcrumb(path: string): NavigationConfig {
    return {
      type: NavigationComponentType.BREADCRUMB,
      props: { links: [...] }
    }
  }
}
```

## 📁 文件结构

```
src/
├── types/
│   └── navigation.ts              # 导航类型定义
├── components/layout/
│   ├── app-header.tsx            # 重构后的 AppHeader
│   ├── navigation-renderer.tsx   # 导航渲染器
│   ├── breadcrumb.tsx           # 面包屑组件
│   ├── tab-nav.tsx              # 标签导航组件
│   └── custom-nav-example.tsx   # 自定义导航示例
└── utils/
    └── navigation-config-factory.ts # 配置工厂
```

## 🔧 使用示例

### 1. Dashboard 页面 - TopNav

```typescript
// 自动配置，显示概览、客户、产品等标签
const config = {
  type: NavigationComponentType.TOP_NAV,
  props: {
    links: [
      { title: '概览', href: '/dashboard', isActive: true },
      { title: '客户', href: '/customers', disabled: true },
      { title: '产品', href: '/products', disabled: true },
    ]
  }
}
```

### 2. Settings 页面 - Breadcrumb

```typescript
// 根据当前路径自动生成面包屑
const config = {
  type: NavigationComponentType.BREADCRUMB,
  props: {
    links: [
      { title: '设置', href: '/settings' },
      { title: '账户设置' } // 当前页面
    ]
  }
}
```

### 3. Tasks 页面 - TabNav

```typescript
// 带徽章的标签导航
const config = {
  type: NavigationComponentType.TAB_NAV,
  props: {
    links: [
      { title: '任务', href: '/tasks', isActive: true },
      { title: '全部', href: '/tasks?filter=all', badge: '12' },
      { title: '进行中', href: '/tasks?filter=progress', badge: '5' },
    ],
    variant: 'underline'
  }
}
```

### 4. 自定义导航组件

```typescript
// 使用自定义组件
const config = {
  type: NavigationComponentType.CUSTOM,
  config: {
    component: CustomNavExample,
    props: {
      items: [...],
      showSearch: true,
      variant: 'horizontal'
    }
  }
}
```

### 5. 无导航

```typescript
// 简单页面不显示任何导航
const config = {
  type: NavigationComponentType.NONE
}
```

## 🎨 组件特性

### TopNav 组件
- 水平排列的导航链接
- 支持激活状态和禁用状态
- 响应式设计

### Breadcrumb 组件
- 面包屑导航路径
- 可自定义分隔符
- 支持链接和纯文本项

### TabNav 组件
- 三种样式变体：default、pills、underline
- 支持图标和徽章
- 支持激活状态和禁用状态

### 自定义组件
- 完全自定义的导航组件
- 支持复杂的交互逻辑
- 可以包含搜索、下拉菜单等功能

## 🚀 扩展指南

### 添加新的导航组件类型

1. **定义类型**
```typescript
// 在 navigation.ts 中添加
enum NavigationComponentType {
  // ... 现有类型
  MEGA_MENU = 'megaMenu',
}

interface MegaMenuProps {
  sections: MegaMenuSection[]
  columns?: number
}
```

2. **创建组件**
```typescript
// components/layout/mega-menu.tsx
export function MegaMenu({ sections, columns = 3 }: MegaMenuProps) {
  // 实现大型菜单组件
}
```

3. **更新渲染器**
```typescript
// 在 navigation-renderer.tsx 中添加
case NavigationComponentType.MEGA_MENU:
  return <MegaMenu {...config.props} />
```

4. **添加配置工厂方法**
```typescript
// 在 navigation-config-factory.ts 中添加
createMegaMenu(): NavigationConfig {
  return {
    type: NavigationComponentType.MEGA_MENU,
    props: { sections: [...] }
  }
}
```

### 创建页面特定的导航配置

```typescript
// utils/page-specific-configs.ts
export function createProductPageNav(t: TFunction): NavigationConfig {
  return {
    type: NavigationComponentType.TAB_NAV,
    props: {
      links: [
        { title: t('products.overview'), href: '/products' },
        { title: t('products.catalog'), href: '/products/catalog' },
        { title: t('products.inventory'), href: '/products/inventory' },
      ],
      variant: 'pills'
    }
  }
}
```

## 📊 重构效果

### 代码简化
- AppHeader 组件从 157 行减少到 50 行
- 配置逻辑模块化，易于维护
- 类型安全，减少运行时错误

### 灵活性提升
- 支持 4+ 种导航组件类型
- 可以轻松添加新的导航组件
- 配置驱动，无需修改核心代码

### 可维护性
- 单一职责原则：每个组件只负责一种导航类型
- 工厂模式：配置创建逻辑集中管理
- 类型安全：TypeScript 确保配置正确性

## 🎯 最佳实践

1. **使用配置工厂**：通过工厂函数创建配置，避免重复代码
2. **类型安全优先**：充分利用 TypeScript 的类型检查
3. **组件职责单一**：每个导航组件只负责一种特定的导航模式
4. **配置驱动**：通过配置而不是硬编码来控制行为
5. **易于测试**：每个组件都可以独立测试

这个重构方案提供了一个灵活、可扩展、类型安全的导航系统，可以满足各种复杂的导航需求。
