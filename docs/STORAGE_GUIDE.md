# Storage 工具类使用指南

本文档介绍如何使用 Shadcn Admin Dashboard 项目中的 localStorage 工具类。

## 概述

项目提供了两个主要的存储工具类：

1. **Storage** - 通用的 localStorage 工具类
2. **AuthStorage** - 专门用于认证数据的存储工具类

## Storage 通用工具类

### 基本用法

```typescript
import { storage } from '@/utils/storage'

// 存储数据
storage.set('key', 'value')
storage.set('user', { name: '<PERSON>', age: 30 })

// 获取数据
const value = storage.get<string>('key')
const user = storage.get<{ name: string; age: number }>('user')

// 删除数据
storage.remove('key')

// 检查键是否存在
if (storage.has('key')) {
  console.log('Key exists')
}
```

### 高级功能

#### 命名空间

```typescript
// 使用自定义命名空间
storage.set('config', { theme: 'dark' }, { namespace: 'app' })
const config = storage.get('config', undefined, { namespace: 'app' })

// 获取命名空间下的所有键
const keys = storage.keys('app')

// 清除命名空间下的所有数据
storage.clear('app')
```

#### 过期时间

```typescript
// 设置 1 小时后过期
storage.set('temp-data', 'value', { expires: 60 * 60 * 1000 })

// 设置 1 天后过期
storage.set('cache', data, { expires: 24 * 60 * 60 * 1000 })
```

#### 统计信息

```typescript
// 获取存储统计
const stats = storage.getStats('app')
console.log(stats.keyCount, stats.totalSize)

// 获取存储大小
const size = storage.getSize('app')

// 清理过期数据
const cleanedCount = storage.cleanup('app')
```

### 配置选项

```typescript
import { Storage } from '@/utils/storage'

const customStorage = new Storage({
  defaultNamespace: 'myapp',
  version: '2.0.0',
  debug: true, // 开启调试模式
})
```

## AuthStorage 认证工具类

### 基本用法

```typescript
import AuthStorage from '@/utils/auth-storage'

// 存储登录信息
const user = {
  id: '1',
  email: '<EMAIL>',
  firstName: 'John',
  lastName: 'Doe',
  // ... 其他用户信息
}
const accessToken = 'your-access-token'
const refreshToken = 'your-refresh-token'

AuthStorage.setLoginInfo(user, accessToken, refreshToken)

// 获取认证信息
const authInfo = AuthStorage.getAuthInfo()
console.log(authInfo.user, authInfo.accessToken, authInfo.isValid)
```

### Token 管理

```typescript
// 单独管理 token
AuthStorage.setAccessToken('new-token')
const token = AuthStorage.getAccessToken()

AuthStorage.setRefreshToken('refresh-token')
const refreshToken = AuthStorage.getRefreshToken()

// 检查是否需要刷新 token
if (AuthStorage.shouldRefreshToken()) {
  // 执行 token 刷新逻辑
}

// 获取 token 剩余时间
const remainingTime = AuthStorage.getTokenRemainingTime()
```

### 用户数据管理

```typescript
// 存储用户数据
AuthStorage.setUserData(user)

// 获取用户数据
const userData = AuthStorage.getUserData()

// 获取用户显示信息
const displayName = AuthStorage.getUserDisplayName() // "John Doe"
const initials = AuthStorage.getUserInitials() // "JD"
```

### 权限检查

```typescript
// 检查用户权限
const hasAdminAccess = AuthStorage.hasPermission(['admin'])
const hasUserAccess = AuthStorage.hasPermission(['user', 'member'])

// 便捷的权限检查方法
const isAdmin = AuthStorage.isAdmin()
const isSuperAdmin = AuthStorage.isSuperAdmin()
```

### 认证状态管理

```typescript
// 检查认证数据是否有效
const isValid = AuthStorage.isAuthDataValid()

// 更新最后活动时间
AuthStorage.updateLastActivity()

// 清除所有认证数据
AuthStorage.clearAll()
```

## 在组件中使用

### 使用 useAuth Hook

```typescript
import { useAuth } from '@/hooks/use-auth'

function MyComponent() {
  const {
    user,
    isAuthenticated,
    userDisplayName,
    userInitials,
    hasPermission,
    isAdmin,
    updateLastActivity,
  } = useAuth()

  // 检查权限
  if (!hasPermission(['admin'])) {
    return <div>Access denied</div>
  }

  // 更新活动时间
  const handleUserAction = () => {
    updateLastActivity()
    // 执行其他逻辑
  }

  return (
    <div>
      <h1>Welcome, {userDisplayName}</h1>
      <div>Avatar: {userInitials}</div>
      {isAdmin && <AdminPanel />}
    </div>
  )
}
```

### 直接使用 AuthStorage

```typescript
import AuthStorage from '@/utils/auth-storage'

function LoginComponent() {
  const handleLogin = async (credentials) => {
    try {
      const response = await loginApi(credentials)
      
      // 存储认证信息
      AuthStorage.setLoginInfo(
        response.user,
        response.accessToken,
        response.refreshToken
      )
      
      // 重定向到 dashboard
      navigate('/dashboard')
    } catch (error) {
      console.error('Login failed:', error)
    }
  }

  return (
    // 登录表单 JSX
  )
}
```

## 最佳实践

### 1. 类型安全

```typescript
// 定义数据类型
interface UserPreferences {
  theme: 'light' | 'dark'
  language: 'en' | 'zh'
  notifications: boolean
}

// 使用类型安全的存储
storage.set<UserPreferences>('preferences', {
  theme: 'dark',
  language: 'zh',
  notifications: true
})

const preferences = storage.get<UserPreferences>('preferences')
```

### 2. 错误处理

```typescript
// 提供默认值
const config = storage.get('config', { theme: 'light' })

// 检查数据是否存在
if (storage.has('important-data')) {
  const data = storage.get('important-data')
  // 处理数据
}
```

### 3. 命名空间管理

```typescript
// 为不同功能使用不同的命名空间
const APP_NAMESPACES = {
  USER_PREFERENCES: 'user-prefs',
  CACHE: 'cache',
  TEMP: 'temp',
} as const

// 使用命名空间
storage.set('theme', 'dark', { namespace: APP_NAMESPACES.USER_PREFERENCES })
```

### 4. 过期时间常量

```typescript
// 定义过期时间常量
const EXPIRY_TIMES = {
  HOUR: 60 * 60 * 1000,
  DAY: 24 * 60 * 60 * 1000,
  WEEK: 7 * 24 * 60 * 60 * 1000,
} as const

// 使用常量
storage.set('cache-data', data, { expires: EXPIRY_TIMES.HOUR })
```

### 5. 定期清理

```typescript
// 在应用启动时清理过期数据
useEffect(() => {
  const cleanupExpiredData = () => {
    storage.cleanup() // 清理默认命名空间
    storage.cleanup('cache') // 清理缓存命名空间
    AuthStorage.cleanup() // 清理认证数据
  }

  cleanupExpiredData()
  
  // 设置定期清理
  const interval = setInterval(cleanupExpiredData, 60 * 60 * 1000) // 每小时清理一次
  
  return () => clearInterval(interval)
}, [])
```

## 浏览器兼容性

工具类会自动检测 localStorage 的可用性：

- 如果 localStorage 可用，正常使用
- 如果 localStorage 不可用（如隐私模式），自动降级到内存存储
- 提供一致的 API，无需担心兼容性问题

## 调试

开启调试模式可以在控制台看到存储操作的详细信息：

```typescript
const debugStorage = new Storage({
  debug: true, // 开启调试模式
})

// 或者在开发环境自动开启
const storage = new Storage({
  debug: process.env.NODE_ENV === 'development',
})
```

## 单元测试

项目提供了完整的单元测试示例，位于：

- `src/utils/__tests__/storage.test.ts`
- `src/utils/__tests__/auth-storage.test.ts`

运行测试：

```bash
npm test storage
```

## 总结

Storage 工具类提供了：

- ✅ 类型安全的 localStorage 操作
- ✅ 自动 JSON 序列化/反序列化
- ✅ 过期时间支持
- ✅ 命名空间管理
- ✅ 错误处理和降级方案
- ✅ 统计信息和清理功能
- ✅ 完整的单元测试覆盖

AuthStorage 专门为认证场景优化，提供了：

- ✅ 完整的认证数据管理
- ✅ Token 生命周期管理
- ✅ 权限检查功能
- ✅ 用户信息处理
- ✅ 安全的数据清理

使用这些工具类可以大大简化本地存储的管理，提高代码的可维护性和安全性。
