import { useTranslation } from 'react-i18next'
import { IconCheck, IconLanguage } from '@tabler/icons-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import type { Language, LanguageOption } from '@/types/i18n'

const languages: LanguageOption[] = [
  {
    value: 'zh-CN',
    label: '中文',
    flag: '🇨🇳',
  },
  {
    value: 'en-US',
    label: 'English',
    flag: '🇺🇸',
  },
]

export function LanguageSwitch() {
  const { i18n, t } = useTranslation()

  const handleLanguageChange = (language: Language) => {
    i18n.changeLanguage(language)
    localStorage.setItem('language', language)
  }

  const currentLanguage = languages.find(
    (lang) => lang.value === i18n.language
  ) || languages[0]

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant='ghost'
          size='icon'
          className='h-8 w-8'
          aria-label={t('language.switchLanguage')}
        >
          <IconLanguage className='h-4 w-4' />
          <span className='sr-only'>{t('language.switchLanguage')}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end' className='w-40'>
        {languages.map((language) => (
          <DropdownMenuItem
            key={language.value}
            onClick={() => handleLanguageChange(language.value)}
            className='flex items-center justify-between'
          >
            <div className='flex items-center gap-2'>
              <span className='text-base'>{language.flag}</span>
              <span>{language.label}</span>
            </div>
            <IconCheck
              className={cn(
                'h-4 w-4',
                currentLanguage.value === language.value
                  ? 'opacity-100'
                  : 'opacity-0'
              )}
            />
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
