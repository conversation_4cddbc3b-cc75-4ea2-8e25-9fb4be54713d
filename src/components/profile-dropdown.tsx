import { Link } from '@tanstack/react-router'
import { useTranslation } from 'react-i18next'
import { IconLogout, IconSettings, IconUser, IconCreditCard, IconUsers } from '@tabler/icons-react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useAuth } from '@/hooks/use-auth'

export function ProfileDropdown() {
  const { t } = useTranslation()
  const { user, userDisplayName, userInitials, logout, isAuthenticated } = useAuth()

  // 如果未认证，不显示下拉菜单
  if (!isAuthenticated || !user) {
    return null
  }

  const handleLogout = async () => {
    await logout()
  }

  return (
    <DropdownMenu modal={false}>
      <DropdownMenuTrigger asChild>
        <Button variant='ghost' className='relative h-8 w-8 rounded-full'>
          <Avatar className='h-8 w-8'>
            <AvatarImage src={user.avatar || '/avatars/01.png'} alt={`@${user.username}`} />
            <AvatarFallback>{userInitials}</AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className='w-56' align='end' forceMount>
        <DropdownMenuLabel className='font-normal'>
          <div className='flex flex-col space-y-1'>
            <p className='text-sm leading-none font-medium'>{userDisplayName}</p>
            <p className='text-muted-foreground text-xs leading-none'>
              {user.email}
            </p>
            {user.role && user.role.length > 0 && (
              <p className='text-muted-foreground text-xs leading-none'>
                {user.role.join(', ')}
              </p>
            )}
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem asChild>
            <Link to='/dashboard/settings'>
              <IconUser className='mr-2 h-4 w-4' />
              {t('navigation.profile')}
              <DropdownMenuShortcut>⇧⌘P</DropdownMenuShortcut>
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild>
            <Link to='/dashboard/settings/account'>
              <IconCreditCard className='mr-2 h-4 w-4' />
              {t('navigation.account')}
              <DropdownMenuShortcut>⌘B</DropdownMenuShortcut>
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild>
            <Link to='/dashboard/settings'>
              <IconSettings className='mr-2 h-4 w-4' />
              {t('navigation.settings')}
              <DropdownMenuShortcut>⌘S</DropdownMenuShortcut>
            </Link>
          </DropdownMenuItem>
          {user.role.includes('admin') && (
            <DropdownMenuItem asChild>
              <Link to='/dashboard/users'>
                <IconUsers className='mr-2 h-4 w-4' />
                {t('navigation.users')}
              </Link>
            </DropdownMenuItem>
          )}
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleLogout}>
          <IconLogout className='mr-2 h-4 w-4' />
          {t('navigation.signOut')}
          <DropdownMenuShortcut>⇧⌘Q</DropdownMenuShortcut>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
