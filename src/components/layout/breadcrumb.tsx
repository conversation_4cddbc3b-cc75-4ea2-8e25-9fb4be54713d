import { Fragment, ReactNode } from 'react'
import { Link } from '@tanstack/react-router'
import { IconChevronRight } from '@tabler/icons-react'
import { cn } from '@/lib/utils'
import type { BreadcrumbProps, BreadcrumbLink } from '@/types/navigation'

interface BreadcrumbItemProps {
  link: BreadcrumbLink
  isLast: boolean
}

function BreadcrumbItem({ link, isLast }: BreadcrumbItemProps) {
  const baseClasses = 'text-sm transition-colors'
  
  if (isLast || !link.href) {
    return (
      <span className={cn(baseClasses, 'text-foreground font-medium')}>
        {link.title}
      </span>
    )
  }

  return (
    <Link
      to={link.href}
      className={cn(
        baseClasses,
        'text-muted-foreground hover:text-foreground'
      )}
    >
      {link.title}
    </Link>
  )
}

export function Breadcrumb({ 
  links, 
  separator = <IconChevronRight className="h-4 w-4" /> 
}: BreadcrumbProps) {
  if (!links || links.length === 0) {
    return null
  }

  return (
    <nav aria-label="Breadcrumb" className="flex items-center space-x-2">
      {links.map((link, index) => {
        const isLast = index === links.length - 1
        
        return (
          <Fragment key={`${link.href}-${index}`}>
            <BreadcrumbItem link={link} isLast={isLast} />
            {!isLast && (
              <span className="text-muted-foreground" aria-hidden="true">
                {separator}
              </span>
            )}
          </Fragment>
        )
      })}
    </nav>
  )
}
