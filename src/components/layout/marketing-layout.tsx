import { Outlet } from '@tanstack/react-router'
import { MarketingHeader } from './marketing-header'
import { MarketingFooter } from './marketing-footer'

interface MarketingLayoutProps {
  children?: React.ReactNode
}

export function MarketingLayout({ children }: MarketingLayoutProps) {
  return (
    <div className="min-h-screen flex flex-col">
      <MarketingHeader />
      <main className="flex-1">
        {children ? children : <Outlet />}
      </main>
      <MarketingFooter />
    </div>
  )
}
