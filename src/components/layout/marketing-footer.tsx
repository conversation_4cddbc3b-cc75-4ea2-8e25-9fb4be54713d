import { Link } from '@tanstack/react-router'
import { useTranslation } from 'react-i18next'
import { IconBrandGithub, IconBrandTwitter, IconBrandLinkedin } from '@tabler/icons-react'

export function MarketingFooter() {
  const { t } = useTranslation()

  const footerLinks = {
    product: [
      { title: t('marketing.features'), href: '/features' },
      { title: t('marketing.pricing'), href: '/pricing' },
      { title: t('marketing.documentation'), href: '/docs' },
      { title: t('marketing.changelog'), href: '/changelog' },
    ],
    company: [
      { title: t('marketing.about'), href: '/about' },
      { title: t('marketing.blog'), href: '/blog' },
      { title: t('marketing.careers'), href: '/careers' },
      { title: t('marketing.contact'), href: '/contact' },
    ],
    resources: [
      { title: t('marketing.helpCenter'), href: '/help' },
      { title: t('marketing.community'), href: '/community' },
      { title: t('marketing.support'), href: '/support' },
      { title: t('marketing.status'), href: '/status' },
    ],
    legal: [
      { title: t('marketing.privacy'), href: '/privacy' },
      { title: t('marketing.terms'), href: '/terms' },
      { title: t('marketing.security'), href: '/security' },
      { title: t('marketing.cookies'), href: '/cookies' },
    ],
  }

  const socialLinks = [
    {
      name: 'GitHub',
      href: 'https://github.com/shadcn-ui/ui',
      icon: IconBrandGithub,
    },
    {
      name: 'Twitter',
      href: 'https://twitter.com/shadcn',
      icon: IconBrandTwitter,
    },
    {
      name: 'LinkedIn',
      href: 'https://linkedin.com/company/shadcn',
      icon: IconBrandLinkedin,
    },
  ]

  return (
    <footer className="border-t bg-background">
      <div className="container py-12 md:py-16">
        <div className="grid grid-cols-2 gap-8 md:grid-cols-5">
          {/* Brand */}
          <div className="col-span-2 md:col-span-1">
            <Link to="/" className="flex items-center space-x-2 mb-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-6 w-6"
              >
                <path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3" />
              </svg>
              <span className="font-bold text-lg">Shadcn Admin</span>
            </Link>
            <p className="text-sm text-muted-foreground mb-4">
              {t('marketing.footerDescription')}
            </p>
            <div className="flex space-x-4">
              {socialLinks.map((social) => {
                const Icon = social.icon
                return (
                  <a
                    key={social.name}
                    href={social.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-muted-foreground hover:text-foreground transition-colors"
                    aria-label={social.name}
                  >
                    <Icon className="h-5 w-5" />
                  </a>
                )
              })}
            </div>
          </div>

          {/* Product */}
          <div>
            <h3 className="font-semibold mb-4">{t('marketing.product')}</h3>
            <ul className="space-y-2">
              {footerLinks.product.map((link) => (
                <li key={link.href}>
                  <Link
                    to={link.href}
                    className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                  >
                    {link.title}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Company */}
          <div>
            <h3 className="font-semibold mb-4">{t('marketing.company')}</h3>
            <ul className="space-y-2">
              {footerLinks.company.map((link) => (
                <li key={link.href}>
                  <Link
                    to={link.href}
                    className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                  >
                    {link.title}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Resources */}
          <div>
            <h3 className="font-semibold mb-4">{t('marketing.resources')}</h3>
            <ul className="space-y-2">
              {footerLinks.resources.map((link) => (
                <li key={link.href}>
                  <Link
                    to={link.href}
                    className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                  >
                    {link.title}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Legal */}
          <div>
            <h3 className="font-semibold mb-4">{t('marketing.legal')}</h3>
            <ul className="space-y-2">
              {footerLinks.legal.map((link) => (
                <li key={link.href}>
                  <Link
                    to={link.href}
                    className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                  >
                    {link.title}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        <div className="mt-12 pt-8 border-t flex flex-col md:flex-row justify-between items-center">
          <p className="text-sm text-muted-foreground">
            © 2024 Shadcn Admin. {t('marketing.allRightsReserved')}
          </p>
          <p className="text-sm text-muted-foreground mt-2 md:mt-0">
            {t('marketing.builtWith')} ❤️ {t('marketing.usingReact')}
          </p>
        </div>
      </div>
    </footer>
  )
}
