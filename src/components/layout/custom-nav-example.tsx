import { useState } from 'react'
import { Link } from '@tanstack/react-router'
import { IconChevronDown, IconSearch } from '@tabler/icons-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

interface CustomNavItem {
  title: string
  href?: string
  children?: CustomNavItem[]
  isActive?: boolean
}

interface CustomNavProps {
  items: CustomNavItem[]
  showSearch?: boolean
  variant?: 'horizontal' | 'vertical'
}

/**
 * 自定义导航组件示例
 * 展示如何创建复杂的导航组件并集成到 AppHeader 中
 */
export function CustomNavExample({ 
  items, 
  showSearch = false, 
  variant = 'horizontal' 
}: CustomNavProps) {
  const [searchTerm, setSearchTerm] = useState('')

  const renderNavItem = (item: CustomNavItem, index: number) => {
    // 如果有子项，渲染下拉菜单
    if (item.children && item.children.length > 0) {
      return (
        <DropdownMenu key={index}>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className={cn(
                'flex items-center gap-1',
                item.isActive && 'bg-accent text-accent-foreground'
              )}
            >
              {item.title}
              <IconChevronDown className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            {item.children.map((child, childIndex) => (
              <DropdownMenuItem key={childIndex} asChild>
                <Link to={child.href || '#'} className="w-full">
                  {child.title}
                </Link>
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      )
    }

    // 普通导航项
    return (
      <Button
        key={index}
        variant={item.isActive ? 'default' : 'ghost'}
        asChild
      >
        <Link to={item.href || '#'}>
          {item.title}
        </Link>
      </Button>
    )
  }

  const containerClasses = cn(
    'flex items-center gap-2',
    variant === 'vertical' && 'flex-col items-start'
  )

  return (
    <div className={containerClasses}>
      {/* 搜索框（可选） */}
      {showSearch && (
        <div className="relative">
          <IconSearch className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="搜索导航..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8 w-48"
          />
        </div>
      )}

      {/* 导航项 */}
      <div className={cn(
        'flex gap-1',
        variant === 'vertical' && 'flex-col w-full'
      )}>
        {items
          .filter(item => 
            !searchTerm || 
            item.title.toLowerCase().includes(searchTerm.toLowerCase())
          )
          .map(renderNavItem)}
      </div>
    </div>
  )
}
