import { useState } from 'react'
import { Link } from '@tanstack/react-router'
import { useTranslation } from 'react-i18next'
import { IconMenu2, IconX } from '@tabler/icons-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { LanguageSwitch } from '@/components/language-switch'
import { ThemeSwitch } from '@/components/theme-switch'
import { useAuth } from '@/hooks/use-auth'

interface NavLink {
  title: string
  href: string
  external?: boolean
}

export function MarketingHeader() {
  const { t } = useTranslation()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const { isAuthenticated, userDisplayName } = useAuth()

  const navLinks: NavLink[] = [
    { title: t('marketing.features'), href: '/features' },
    { title: t('marketing.pricing'), href: '/pricing' },
    { title: t('marketing.blog'), href: '/blog' },
    { title: t('marketing.about'), href: '/about' },
    { title: t('marketing.contact'), href: '/contact' },
  ]

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen)
  }

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center justify-between">
        {/* Logo */}
        <Link to="/" className="flex items-center space-x-2">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="h-6 w-6"
          >
            <path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3" />
          </svg>
          <span className="font-bold text-xl">Shadcn Admin</span>
        </Link>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center space-x-6">
          {navLinks.map((link) => (
            <Link
              key={link.href}
              to={link.href}
              className="text-sm font-medium text-muted-foreground transition-colors hover:text-foreground"
              activeProps={{
                className: "text-foreground"
              }}
            >
              {link.title}
            </Link>
          ))}
        </nav>

        {/* Desktop Actions */}
        <div className="hidden md:flex items-center space-x-4">
          <LanguageSwitch />
          <ThemeSwitch />

          {isAuthenticated ? (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">
                {t('common.welcome')}, {userDisplayName}
              </span>
              <Button asChild>
                <Link to="/dashboard">
                  {t('marketing.dashboard')}
                </Link>
              </Button>
            </div>
          ) : (
            <div className="flex items-center space-x-2">
              <Button variant="ghost" asChild>
                <Link to="/sign-in">
                  {t('navigation.signIn')}
                </Link>
              </Button>
              <Button asChild>
                <Link to="/sign-up">
                  {t('marketing.getStarted')}
                </Link>
              </Button>
            </div>
          )}
        </div>

        {/* Mobile Menu Button */}
        <div className="flex md:hidden items-center space-x-2">
          <LanguageSwitch />
          <ThemeSwitch />
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleMobileMenu}
            aria-label="Toggle menu"
          >
            {isMobileMenuOpen ? (
              <IconX className="h-5 w-5" />
            ) : (
              <IconMenu2 className="h-5 w-5" />
            )}
          </Button>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMobileMenuOpen && (
        <div className="md:hidden border-t bg-background">
          <div className="container py-4 space-y-4">
            <nav className="flex flex-col space-y-3">
              {navLinks.map((link) => (
                <Link
                  key={link.href}
                  to={link.href}
                  className="text-sm font-medium text-muted-foreground transition-colors hover:text-foreground"
                  onClick={() => setIsMobileMenuOpen(false)}
                  activeProps={{
                    className: "text-foreground"
                  }}
                >
                  {link.title}
                </Link>
              ))}
            </nav>

            <div className="flex flex-col space-y-2 pt-4 border-t">
              {isAuthenticated ? (
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground text-center">
                    {t('common.welcome')}, {userDisplayName}
                  </p>
                  <Button asChild className="w-full">
                    <Link to="/dashboard" onClick={() => setIsMobileMenuOpen(false)}>
                      {t('marketing.dashboard')}
                    </Link>
                  </Button>
                </div>
              ) : (
                <>
                  <Button variant="ghost" asChild className="w-full">
                    <Link to="/sign-in" onClick={() => setIsMobileMenuOpen(false)}>
                      {t('navigation.signIn')}
                    </Link>
                  </Button>
                  <Button asChild className="w-full">
                    <Link to="/sign-up" onClick={() => setIsMobileMenuOpen(false)}>
                      {t('marketing.getStarted')}
                    </Link>
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
      )}
    </header>
  )
}
