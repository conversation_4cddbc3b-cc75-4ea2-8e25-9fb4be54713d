import { useLocation } from '@tanstack/react-router'
import { useTranslation } from 'react-i18next'
import { Header } from '@/components/layout/header'
import { NavigationWrapper } from '@/components/layout/navigation-renderer'
import { LanguageSwitch } from '@/components/language-switch'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { createNavigationConfig } from '@/utils/navigation-config-factory'
import type { HeaderConfig } from '@/types/navigation'

interface AppHeaderProps {
  className?: string
}

export function AppHeader({ className }: AppHeaderProps) {
  const { t } = useTranslation()
  const location = useLocation()

  // 根据当前路由决定 Header 配置
  const getHeaderConfig = (): HeaderConfig => {
    const pathname = location.pathname

    // 使用配置工厂创建导航配置
    const navigation = createNavigationConfig(pathname, t)

    // 根据路径决定是否固定 Header
    const getFixedConfig = (path: string): boolean => {

      if (path.startsWith('/dashboard/tasks')) return true
      if (path.startsWith('/dashboard/users')) return true
      if (path.startsWith('/dashboard/apps')) return true
      return false
    }
    
    return {
      fixed: getFixedConfig(pathname),
      navigation
    }
  }

  const { fixed, navigation } = getHeaderConfig()

  return (
    <Header fixed={fixed} className={className}>
      <NavigationWrapper config={navigation} />
      <div className='ml-auto flex items-center space-x-4'>
        <Search />
        <LanguageSwitch />
        <ThemeSwitch />
        <ProfileDropdown />
      </div>
    </Header>
  )
}
