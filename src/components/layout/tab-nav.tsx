import { Link } from '@tanstack/react-router'
import { cn } from '@/lib/utils'
import { Badge } from '@/components/ui/badge'
import type { TabNavProps, TabNavLink } from '@/types/navigation'

interface TabNavItemProps {
  link: TabNavLink
  variant: 'default' | 'pills' | 'underline'
}

function TabNavItem({ link, variant }: TabNavItemProps) {
  const baseClasses = 'inline-flex items-center gap-2 px-3 py-2 text-sm font-medium transition-colors'
  
  const variantClasses = {
    default: cn(
      'rounded-md',
      link.isActive
        ? 'bg-primary text-primary-foreground'
        : 'text-muted-foreground hover:text-foreground hover:bg-muted'
    ),
    pills: cn(
      'rounded-full',
      link.isActive
        ? 'bg-primary text-primary-foreground'
        : 'text-muted-foreground hover:text-foreground hover:bg-muted'
    ),
    underline: cn(
      'border-b-2 rounded-none',
      link.isActive
        ? 'border-primary text-foreground'
        : 'border-transparent text-muted-foreground hover:text-foreground hover:border-muted-foreground'
    ),
  }

  const content = (
    <>
      {link.icon && <span className="h-4 w-4">{link.icon}</span>}
      <span>{link.title}</span>
      {link.badge && (
        <Badge variant="secondary" className="ml-1 h-5 px-1.5 text-xs">
          {link.badge}
        </Badge>
      )}
    </>
  )

  if (link.disabled) {
    return (
      <span className={cn(baseClasses, variantClasses[variant], 'opacity-50 cursor-not-allowed')}>
        {content}
      </span>
    )
  }

  return (
    <Link
      to={link.href}
      className={cn(baseClasses, variantClasses[variant])}
      aria-current={link.isActive ? 'page' : undefined}
    >
      {content}
    </Link>
  )
}

export function TabNav({ links, variant = 'default' }: TabNavProps) {
  if (!links || links.length === 0) {
    return null
  }

  const containerClasses = {
    default: 'flex items-center gap-1',
    pills: 'flex items-center gap-1',
    underline: 'flex items-center border-b border-border',
  }

  return (
    <nav className={containerClasses[variant]} role="tablist">
      {links.map((link, index) => (
        <TabNavItem
          key={`${link.href}-${index}`}
          link={link}
          variant={variant}
        />
      ))}
    </nav>
  )
}
