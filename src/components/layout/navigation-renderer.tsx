import { ReactNode } from 'react'
import { TopNav } from './top-nav'
import { Breadcrumb } from './breadcrumb'
import { TabNav } from './tab-nav'
import { NavigationComponentType, type NavigationConfig } from '@/types/navigation'

/**
 * 导航组件渲染器
 * 根据配置渲染对应的导航组件
 */
export function renderNavigation(config: NavigationConfig): ReactNode {
  switch (config.type) {
    case NavigationComponentType.NONE:
      return null

    case NavigationComponentType.TOP_NAV:
      return <TopNav {...config.props} links={config.props.links.map(link => ({
        ...link,
        isActive: link.isActive ?? false
      }))} />

    case NavigationComponentType.BREADCRUMB:
      return <Breadcrumb {...config.props} />

    case NavigationComponentType.TAB_NAV:
      return <TabNav {...config.props} />

    case NavigationComponentType.CUSTOM:
      const { component: CustomComponent, props } = config.config
      return <CustomComponent {...props} />

    default:
      // TypeScript 会确保这里永远不会被执行      
      return null
  }
}

/**
 * 导航组件包装器
 * 提供统一的样式和布局
 */
interface NavigationWrapperProps {
  config: NavigationConfig
  className?: string
}

export function NavigationWrapper({ config, className }: NavigationWrapperProps) {
  const navigation = renderNavigation(config)
  
  if (!navigation) {
    return null
  }

  return (
    <div className={className}>
      {navigation}
    </div>
  )
}
