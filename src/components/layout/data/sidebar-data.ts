import {
  IconBarrierBlock,
  IconBrowserCheck,
  IconBug,
  IconChecklist,
  IconError404,
  IconHelp,
  IconLayoutDashboard,
  IconLock,
  IconLockAccess,
  IconMessages,
  IconNotification,
  IconPackages,
  IconPalette,
  IconServerOff,
  IconSettings,
  IconTool,
  IconUserCog,
  IconUserOff,
  IconUsers,
} from '@tabler/icons-react'
import { AudioWaveform, Command, GalleryVerticalEnd } from 'lucide-react'
import { ClerkLogo } from '@/assets/clerk-logo'
import { type SidebarData } from '../types'

// 创建一个函数来生成侧边栏数据，支持国际化
export const createSidebarData = (t: (key: string) => string): SidebarData => ({
  user: {
    name: 'satnaing',
    email: '<EMAIL>',
    avatar: '/avatars/shadcn.jpg',
  },
  teams: [
    {
      name: 'Shadcn Admin',
      logo: Command,
      plan: 'Vite + ShadcnUI',
    },
    {
      name: 'Acme Inc',
      logo: GalleryVerticalEnd,
      plan: 'Enterprise',
    },
    {
      name: 'Acme Corp.',
      logo: AudioWaveform,
      plan: 'Startup',
    },
  ],
  navGroups: [
    {
      title: t('navigation.general'),
      items: [
        {
          title: t('navigation.dashboard'),
          url: '/dashboard/overview',
          icon: IconLayoutDashboard,
        },
        {
          title: t('navigation.tasks'),
          url: '/dashboard/tasks',
          icon: IconChecklist,
        },
        {
          title: t('navigation.apps'),
          url: '/dashboard/apps',
          icon: IconPackages,
        },
        {
          title: t('navigation.chats'),
          url: '/dashboard/chats',
          badge: '3',
          icon: IconMessages,
        },
        {
          title: t('navigation.users'),
          url: '/dashboard/users',
          icon: IconUsers,
        },
        {
          title: t('navigation.securedByClerk'),
          icon: ClerkLogo,
          items: [
            {
              title: t('navigation.signIn'),
              url: '/clerk/sign-in',
            },
            {
              title: t('navigation.signUp'),
              url: '/clerk/sign-up',
            },
            {
              title: t('navigation.userManagement'),
              url: '/clerk/user-management',
            },
          ],
        },
      ],
    },
    {
      title: t('navigation.pages'),
      items: [
        {
          title: t('navigation.auth'),
          icon: IconLockAccess,
          items: [
            {
              title: t('navigation.signIn'),
              url: '/sign-in',
            },
            {
              title: 'Sign In (2 Col)',
              url: '/sign-in-2',
            },
            {
              title: t('navigation.signUp'),
              url: '/sign-up',
            },
            {
              title: t('navigation.forgotPassword'),
              url: '/forgot-password',
            },
            {
              title: t('navigation.otp'),
              url: '/otp',
            },
          ],
        },
        {
          title: t('navigation.errors'),
          icon: IconBug,
          items: [
            {
              title: t('navigation.unauthorized'),
              url: '/401',
              icon: IconLock,
            },
            {
              title: t('navigation.forbidden'),
              url: '/403',
              icon: IconUserOff,
            },
            {
              title: t('navigation.notFound'),
              url: '/404',
              icon: IconError404,
            },
            {
              title: t('navigation.internalServerError'),
              url: '/500',
              icon: IconServerOff,
            },
            {
              title: t('navigation.maintenanceError'),
              url: '/503',
              icon: IconBarrierBlock,
            },
          ],
        },
      ],
    },
    {
      title: t('navigation.other'),
      items: [
        {
          title: t('navigation.settings'),
          icon: IconSettings,
          items: [
            {
              title: t('navigation.profile'),
              url: '/dashboard/settings',
              icon: IconUserCog,
            },
            {
              title: t('navigation.account'),
              url: '/dashboard/settings/account',
              icon: IconTool,
            },
            {
              title: t('navigation.appearance'),
              url: '/dashboard/settings/appearance',
              icon: IconPalette,
            },
            {
              title: t('navigation.notifications'),
              url: '/dashboard/settings/notifications',
              icon: IconNotification,
            },
            {
              title: t('navigation.display'),
              url: '/dashboard/settings/display',
              icon: IconBrowserCheck,
            },
          ],
        },
        {
          title: t('navigation.helpCenter'),
          url: '/dashboard/help-center',
          icon: IconHelp,
        },
      ],
    },
  ],
})

// 保持向后兼容的默认导出（使用英文）
export const sidebarData: SidebarData = createSidebarData((key: string) => {
  // 简单的英文映射，实际使用时会被 i18n 替换
  const fallbackTranslations: Record<string, string> = {
    'navigation.general': 'General',
    'navigation.dashboard': 'Dashboard',
    'navigation.tasks': 'Tasks',
    'navigation.apps': 'Apps',
    'navigation.chats': 'Chats',
    'navigation.users': 'Users',
    'navigation.securedByClerk': 'Secured by Clerk',
    'navigation.signIn': 'Sign In',
    'navigation.signUp': 'Sign Up',
    'navigation.userManagement': 'User Management',
    'navigation.pages': 'Pages',
    'navigation.auth': 'Auth',
    'navigation.forgotPassword': 'Forgot Password',
    'navigation.otp': 'OTP',
    'navigation.errors': 'Errors',
    'navigation.unauthorized': 'Unauthorized',
    'navigation.forbidden': 'Forbidden',
    'navigation.notFound': 'Not Found',
    'navigation.internalServerError': 'Internal Server Error',
    'navigation.maintenanceError': 'Maintenance Error',
    'navigation.other': 'Other',
    'navigation.settings': 'Settings',
    'navigation.profile': 'Profile',
    'navigation.account': 'Account',
    'navigation.appearance': 'Appearance',
    'navigation.notifications': 'Notifications',
    'navigation.display': 'Display',
    'navigation.helpCenter': 'Help Center',
  }
  return fallbackTranslations[key] || key
})
