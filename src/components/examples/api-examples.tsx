/**
 * API 使用示例组件
 * 展示如何使用统一 API 解决方案
 */

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { toast } from 'sonner'
import {
  useApiQuery,
  useApiMutation,
  useApiInfiniteQuery,
  useUpload,
  useDownload,
  useDragUpload,
  useUsers,
  useCreateUser,
  api,
  getAllPagesData,
  getRequestStats,
} from '@/lib/api'

// 基础查询示例
function BasicQueryExample() {
  const { data: users, isLoading, error, refetch } = useApiQuery({
    url: '/users',
    params: { page: 1, pageSize: 5 },
    queryKey: ['users', 'basic'],
  })

  return (
    <Card>
      <CardHeader>
        <CardTitle>基础查询示例</CardTitle>
        <CardDescription>使用 useApiQuery 获取用户列表</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <Button onClick={() => refetch()} disabled={isLoading}>
            {isLoading ? '加载中...' : '刷新数据'}
          </Button>
          <Badge variant={error ? 'destructive' : 'default'}>
            {error ? '错误' : '正常'}
          </Badge>
        </div>
        
        {isLoading && <div>加载中...</div>}
        {error && <div className="text-red-500">错误: {error.message}</div>}
        {users && (
          <div className="space-y-2">
            <div className="text-sm text-muted-foreground">
              共 {users.data?.total || 0} 个用户
            </div>
            {users.data?.items?.map((user: any) => (
              <div key={user.id} className="p-2 border rounded">
                <div className="font-medium">{user.name}</div>
                <div className="text-sm text-muted-foreground">{user.email}</div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

// Mutation 示例
function MutationExample() {
  const [name, setName] = useState('')
  const [email, setEmail] = useState('')

  const createUserMutation = useApiMutation(
    (userData: { name: string; email: string }) => api.post('/users', userData),
    {
      onSuccess: () => {
        setName('')
        setEmail('')
        toast.success('用户创建成功！')
      },
      onError: (error) => {
        toast.error(`创建失败: ${error.message}`)
      },
    }
  )

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (name && email) {
      createUserMutation.mutate({ name, email })
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Mutation 示例</CardTitle>
        <CardDescription>使用 useApiMutation 创建用户</CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="name">姓名</Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="输入姓名"
            />
          </div>
          <div>
            <Label htmlFor="email">邮箱</Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="输入邮箱"
            />
          </div>
          <Button 
            type="submit" 
            disabled={createUserMutation.isPending || !name || !email}
          >
            {createUserMutation.isPending ? '创建中...' : '创建用户'}
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}

// 便捷 Hooks 示例
function ConvenientHooksExample() {
  const { data: users, isLoading } = useUsers({ page: 1, pageSize: 3 })
  const createUser = useCreateUser()

  const handleQuickCreate = () => {
    createUser.mutate({
      name: `用户${Date.now()}`,
      email: `user${Date.now()}@example.com`,
    })
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>便捷 Hooks 示例</CardTitle>
        <CardDescription>使用预定义的业务 Hooks</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Button onClick={handleQuickCreate} disabled={createUser.isPending}>
          {createUser.isPending ? '创建中...' : '快速创建用户'}
        </Button>
        
        {isLoading ? (
          <div>加载中...</div>
        ) : (
          <div className="space-y-2">
            {users?.data.items?.map((user: any) => (
              <div key={user.id} className="p-2 border rounded text-sm">
                {user.name} - {user.email}
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

// 无限查询示例
function InfiniteQueryExample() {
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading
  } = useApiInfiniteQuery({
    url: '/users',
    pageSize: 3,
    queryKey: ['users', 'infinite'],
  })

  const users = getAllPagesData(data)

  return (
    <Card>
      <CardHeader>
        <CardTitle>无限查询示例</CardTitle>
        <CardDescription>无限滚动加载数据</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {isLoading ? (
          <div>加载中...</div>
        ) : (
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {users.map((user: any) => (
              <div key={user.id} className="p-2 border rounded text-sm">
                {user.name}
              </div>
            ))}
          </div>
        )}
        
        {hasNextPage && (
          <Button
            onClick={() => fetchNextPage()}
            disabled={isFetchingNextPage}
            variant="outline"
            size="sm"
          >
            {isFetchingNextPage ? '加载中...' : '加载更多'}
          </Button>
        )}
      </CardContent>
    </Card>
  )
}

// 文件上传示例
function FileUploadExample() {
  const {
    uploadFile,
    isUploading,
    progress,
    uploadedFiles,
    error,
    reset
  } = useUpload({
    endpoint: '/files/upload',
    accept: ['image/*'],
    maxSize: 5 * 1024 * 1024, // 5MB
    onUploadSuccess: (file) => {
      toast.success(`文件 "${file.name}" 上传成功！`)
    }
  })

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      uploadFile(file)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>文件上传示例</CardTitle>
        <CardDescription>支持进度显示的文件上传</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Input
            type="file"
            accept="image/*"
            onChange={handleFileSelect}
            disabled={isUploading}
          />
        </div>
        
        {isUploading && (
          <div className="space-y-2">
            <div className="text-sm">上传中... {progress}%</div>
            <Progress value={progress} />
          </div>
        )}
        
        {error && (
          <div className="text-red-500 text-sm">错误: {error}</div>
        )}
        
        {uploadedFiles.length > 0 && (
          <div className="space-y-2">
            <div className="text-sm font-medium">已上传文件:</div>
            {uploadedFiles.map((file) => (
              <div key={file.id} className="flex items-center gap-2 text-sm">
                <Badge variant="secondary">{file.name}</Badge>
                <span className="text-muted-foreground">
                  {(file.size / 1024).toFixed(1)} KB
                </span>
              </div>
            ))}
            <Button onClick={reset} variant="outline" size="sm">
              清除记录
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

// 拖拽上传示例
function DragUploadExample() {
  const {
    uploadFiles,
    isDragOver,
    dragProps,
    isUploading,
    progress,
    uploadedFiles
  } = useDragUpload({
    multiple: true,
    accept: ['image/*'],
    maxSize: 5 * 1024 * 1024
  })

  return (
    <Card>
      <CardHeader>
        <CardTitle>拖拽上传示例</CardTitle>
        <CardDescription>支持拖拽的多文件上传</CardDescription>
      </CardHeader>
      <CardContent>
        <div
          {...dragProps}
          className={`
            border-2 border-dashed rounded-lg p-8 text-center transition-colors
            ${isDragOver 
              ? 'border-primary bg-primary/5' 
              : 'border-muted-foreground/25'
            }
          `}
        >
          {isUploading ? (
            <div className="space-y-2">
              <div>上传中... {progress}%</div>
              <Progress value={progress} />
            </div>
          ) : (
            <div className="space-y-2">
              <div className="text-muted-foreground">
                拖拽图片文件到这里，或点击选择文件
              </div>
              <div className="text-sm text-muted-foreground">
                支持多文件，最大 5MB
              </div>
            </div>
          )}
        </div>
        
        {uploadedFiles.length > 0 && (
          <div className="mt-4 space-y-2">
            <div className="text-sm font-medium">已上传 {uploadedFiles.length} 个文件</div>
            <div className="grid grid-cols-2 gap-2">
              {uploadedFiles.map((file) => (
                <div key={file.id} className="text-xs p-2 border rounded">
                  {file.name}
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

// 文件下载示例
function FileDownloadExample() {
  const {
    downloadFile,
    isDownloading,
    progress,
    error
  } = useDownload({
    onDownloadSuccess: (blob, filename) => {
      toast.success(`文件 "${filename}" 下载完成！`)
    }
  })

  const handleDownload = () => {
    // 模拟下载一个文件
    downloadFile('/files/sample.pdf', 'sample-document.pdf')
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>文件下载示例</CardTitle>
        <CardDescription>支持进度显示的文件下载</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Button onClick={handleDownload} disabled={isDownloading}>
          {isDownloading ? `下载中... ${progress}%` : '下载示例文件'}
        </Button>
        
        {isDownloading && (
          <div className="space-y-2">
            <Progress value={progress} />
            <div className="text-sm text-muted-foreground">
              下载进度: {progress}%
            </div>
          </div>
        )}
        
        {error && (
          <div className="text-red-500 text-sm">下载失败: {error}</div>
        )}
      </CardContent>
    </Card>
  )
}

// 请求统计示例
function RequestStatsExample() {
  const [stats, setStats] = useState(getRequestStats())

  const refreshStats = () => {
    setStats(getRequestStats())
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>请求统计示例</CardTitle>
        <CardDescription>查看 API 请求统计信息</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Button onClick={refreshStats} variant="outline" size="sm">
          刷新统计
        </Button>
        
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="space-y-1">
            <div className="text-muted-foreground">总请求数</div>
            <div className="font-medium">{stats.total}</div>
          </div>
          <div className="space-y-1">
            <div className="text-muted-foreground">成功请求</div>
            <div className="font-medium text-green-600">{stats.success}</div>
          </div>
          <div className="space-y-1">
            <div className="text-muted-foreground">失败请求</div>
            <div className="font-medium text-red-600">{stats.error}</div>
          </div>
          <div className="space-y-1">
            <div className="text-muted-foreground">平均响应时间</div>
            <div className="font-medium">{stats.averageTime.toFixed(0)}ms</div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// 主组件
export function ApiExamples() {
  return (
    <div className="container py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">API 使用示例</h1>
        <p className="text-muted-foreground">
          展示统一 API 解决方案的各种使用方式和功能特性
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <BasicQueryExample />
        <MutationExample />
        <ConvenientHooksExample />
        <InfiniteQueryExample />
        <FileUploadExample />
        <DragUploadExample />
        <FileDownloadExample />
        <RequestStatsExample />
      </div>
    </div>
  )
}
