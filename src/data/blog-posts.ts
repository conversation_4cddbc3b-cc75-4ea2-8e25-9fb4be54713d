export interface BlogPost {
  id: string
  title: string
  slug: string
  excerpt: string
  content: string
  author: {
    name: string
    avatar: string
    bio?: string
  }
  publishedAt: string
  readTime: number
  category: string
  tags: string[]
  featured: boolean
}



export const blogPosts: BlogPost[] = [
  {
    id: '1',
    title: 'Shadcn Admin v2.0 发布：全新的设计系统',
    slug: 'shadcn-admin-v2-release',
    excerpt: '我们很高兴地宣布 Shadcn Admin v2.0 正式发布！这个版本带来了全新的设计系统、更好的性能和更多的组件。',
    content: `
# Shadcn Admin v2.0 发布

我们很高兴地宣布 **Shadcn Admin v2.0** 正式发布！这个版本是我们迄今为止最大的更新，带来了全新的设计系统、更好的性能和更多的组件。

## 🎨 全新的设计系统

v2.0 版本采用了全新的设计语言，更加现代化和一致性：

- **统一的颜色系统**：基于 OKLCH 颜色空间的全新调色板
- **改进的排版**：更好的字体层次和可读性
- **增强的组件**：所有组件都经过重新设计和优化
- **暗色模式优化**：更好的暗色模式体验

## ⚡ 性能提升

我们在性能方面做了大量优化：

- **更快的加载速度**：减少了 30% 的包体积
- **更好的渲染性能**：优化了组件渲染逻辑
- **懒加载支持**：支持组件和路由的懒加载
- **Tree Shaking**：更好的 Tree Shaking 支持

## 🧩 新增组件

v2.0 版本新增了多个实用组件：

- **数据表格**：功能强大的数据表格组件
- **图表组件**：基于 Recharts 的图表组件库
- **文件上传**：支持拖拽的文件上传组件
- **富文本编辑器**：集成的富文本编辑器

## 🔧 开发体验改进

我们也改进了开发体验：

- **更好的 TypeScript 支持**：完整的类型定义
- **改进的文档**：更详细的文档和示例
- **开发工具**：新的开发工具和调试功能
- **测试支持**：内置的测试工具和示例

## 🚀 如何升级

升级到 v2.0 非常简单：

\`\`\`bash
npm install shadcn-admin@latest
\`\`\`

详细的升级指南请参考我们的[升级文档](/docs/upgrade)。

## 🙏 致谢

感谢所有为这个版本做出贡献的开发者和社区成员。你们的反馈和贡献让 Shadcn Admin 变得更好！

---

如果你有任何问题或建议，欢迎在 [GitHub](https://github.com/shadcn-admin/shadcn-admin) 上提交 Issue 或 PR。
    `,
    author: {
      name: '张三',
      avatar: '/images/avatar-1.jpg',
      bio: 'Shadcn Admin 核心开发者'
    },
    publishedAt: '2024-01-15',
    readTime: 5,
    category: '产品更新',
    tags: ['发布', '设计系统', 'React'],
    featured: true
  },
  {
    id: '2',
    title: '如何使用 React Hook Form 构建高性能表单',
    slug: 'react-hook-form-guide',
    excerpt: '学习如何使用 React Hook Form 和 Zod 验证来构建高性能、类型安全的表单组件。',
    content: `
# 如何使用 React Hook Form 构建高性能表单

React Hook Form 是一个高性能、灵活且易于使用的表单库。结合 Zod 验证，我们可以构建类型安全的表单组件。

## 为什么选择 React Hook Form？

- **性能优异**：最小化重新渲染
- **易于使用**：简洁的 API 设计
- **类型安全**：完整的 TypeScript 支持
- **灵活验证**：支持多种验证方式

## 基础用法

\`\`\`tsx
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'

const schema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
})

type FormData = z.infer<typeof schema>

function LoginForm() {
  const { register, handleSubmit, formState: { errors } } = useForm<FormData>({
    resolver: zodResolver(schema)
  })

  const onSubmit = (data: FormData) => {
    console.log(data)
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <input {...register('email')} />
      {errors.email && <span>{errors.email.message}</span>}

      <input {...register('password')} type="password" />
      {errors.password && <span>{errors.password.message}</span>}

      <button type="submit">登录</button>
    </form>
  )
}
\`\`\`

## 高级技巧

### 1. 自定义验证

\`\`\`tsx
const schema = z.object({
  username: z.string().refine(
    async (username) => {
      // 异步验证用户名是否可用
      const available = await checkUsernameAvailability(username)
      return available
    },
    { message: '用户名已被占用' }
  )
})
\`\`\`

### 2. 条件验证

\`\`\`tsx
const schema = z.object({
  type: z.enum(['personal', 'business']),
  companyName: z.string().optional()
}).refine(
  (data) => data.type !== 'business' || data.companyName,
  {
    message: '企业用户必须填写公司名称',
    path: ['companyName']
  }
)
\`\`\`

## 最佳实践

1. **使用 TypeScript**：充分利用类型安全
2. **合理的验证策略**：平衡用户体验和数据完整性
3. **错误处理**：提供清晰的错误信息
4. **性能优化**：避免不必要的重新渲染

通过这些技巧，你可以构建出既高性能又用户友好的表单组件。
    `,
    author: {
      name: '李四',
      avatar: '/images/avatar-2.jpg',
      bio: '前端开发专家'
    },
    publishedAt: '2024-01-10',
    readTime: 8,
    category: '技术教程',
    tags: ['React', '表单', 'TypeScript'],
    featured: false
  },
  {
    id: '3',
    title: 'Tailwind CSS 最佳实践：构建可维护的样式系统',
    slug: 'tailwind-css-best-practices',
    excerpt: '探索 Tailwind CSS 的最佳实践，学习如何构建可维护、可扩展的样式系统。',
    content: `
# Tailwind CSS 最佳实践

Tailwind CSS 是一个功能优先的 CSS 框架，它提供了构建现代 Web 应用所需的所有工具。

## 核心原则

### 1. 功能优先
使用功能类而不是语义类：

\`\`\`html
<!-- 好的做法 -->
<button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
  按钮
</button>

<!-- 避免的做法 -->
<button class="btn btn-primary">
  按钮
</button>
\`\`\`

### 2. 响应式设计
使用响应式前缀：

\`\`\`html
<div class="w-full md:w-1/2 lg:w-1/3">
  响应式容器
</div>
\`\`\`

## 组织代码

### 1. 使用组件
将重复的样式提取为组件：

\`\`\`tsx
const Button = ({ children, variant = 'primary' }) => {
  const baseClasses = 'font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline'
  const variantClasses = {
    primary: 'bg-blue-500 hover:bg-blue-700 text-white',
    secondary: 'bg-gray-500 hover:bg-gray-700 text-white'
  }

  return (
    <button className={\`\${baseClasses} \${variantClasses[variant]}\`}>
      {children}
    </button>
  )
}
\`\`\`

### 2. 自定义配置
在 \`tailwind.config.js\` 中定义设计系统：

\`\`\`js
module.exports = {
  theme: {
    extend: {
      colors: {
        brand: {
          50: '#eff6ff',
          500: '#3b82f6',
          900: '#1e3a8a'
        }
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem'
      }
    }
  }
}
\`\`\`

## 性能优化

### 1. 清除未使用的样式
配置 PurgeCSS：

\`\`\`js
module.exports = {
  content: [
    './src/**/*.{js,jsx,ts,tsx}',
    './public/index.html'
  ],
  // ...
}
\`\`\`

### 2. 使用 JIT 模式
启用即时编译：

\`\`\`js
module.exports = {
  mode: 'jit',
  // ...
}
\`\`\`

通过遵循这些最佳实践，你可以构建出既美观又可维护的样式系统。
    `,
    author: {
      name: '王五',
      avatar: '/images/avatar-3.jpg',
      bio: 'UI/UX 设计师'
    },
    publishedAt: '2024-01-05',
    readTime: 6,
    category: '技术教程',
    tags: ['CSS', 'Tailwind', '最佳实践'],
    featured: false
  },
  {
    id: '4',
    title: '深入理解 React Server Components',
    slug: 'react-server-components-deep-dive',
    excerpt: 'React Server Components 是 React 的未来。让我们深入了解它们的工作原理和使用场景。',
    content: `
# 深入理解 React Server Components

React Server Components (RSC) 是 React 18 引入的一个革命性特性，它允许组件在服务器端渲染，同时保持客户端的交互性。

## 什么是 Server Components？

Server Components 是在服务器端运行的 React 组件，它们：

- 在服务器端渲染
- 可以直接访问后端资源
- 不会发送到客户端
- 不能使用浏览器 API

## 核心概念

### 1. 组件类型

\`\`\`tsx
// Server Component (默认)
async function BlogPost({ id }) {
  const post = await db.posts.findById(id)
  return <article>{post.content}</article>
}

// Client Component
'use client'
function InteractiveButton() {
  const [count, setCount] = useState(0)
  return <button onClick={() => setCount(count + 1)}>{count}</button>
}
\`\`\`

### 2. 数据获取

Server Components 可以直接访问数据库：

\`\`\`tsx
async function UserProfile({ userId }) {
  // 直接在组件中进行数据库查询
  const user = await db.users.findById(userId)
  const posts = await db.posts.findByUserId(userId)

  return (
    <div>
      <h1>{user.name}</h1>
      <PostList posts={posts} />
    </div>
  )
}
\`\`\`

## 优势

### 1. 性能提升
- 减少客户端 JavaScript 包大小
- 更快的初始页面加载
- 更好的 SEO

### 2. 开发体验
- 直接访问后端资源
- 简化的数据获取
- 更好的代码组织

## 使用场景

### 1. 静态内容
适合展示型组件：

\`\`\`tsx
async function ProductCatalog() {
  const products = await getProducts()
  return (
    <div>
      {products.map(product => (
        <ProductCard key={product.id} product={product} />
      ))}
    </div>
  )
}
\`\`\`

### 2. 数据密集型应用
适合需要大量服务器数据的应用：

\`\`\`tsx
async function Dashboard() {
  const [analytics, users, revenue] = await Promise.all([
    getAnalytics(),
    getUsers(),
    getRevenue()
  ])

  return (
    <div>
      <AnalyticsChart data={analytics} />
      <UserTable users={users} />
      <RevenueWidget revenue={revenue} />
    </div>
  )
}
\`\`\`

## 最佳实践

1. **合理划分组件**：静态内容使用 Server Components，交互功能使用 Client Components
2. **数据获取优化**：在 Server Components 中并行获取数据
3. **错误处理**：使用 Error Boundaries 处理服务器端错误
4. **缓存策略**：合理使用缓存提高性能

React Server Components 代表了 React 应用架构的未来方向，值得深入学习和实践。
    `,
    author: {
      name: '赵六',
      avatar: '/images/avatar-4.jpg',
      bio: 'React 核心贡献者'
    },
    publishedAt: '2024-01-01',
    readTime: 12,
    category: '技术深度',
    tags: ['React', 'Server Components', '架构'],
    featured: true
  }
]



export const blogCategories = [
  '所有分类',
  '产品更新',
  '技术教程',
  '技术深度',
  '设计',
  '最佳实践'
]

// 获取博客文章
export function getBlogPosts(options?: {
  category?: string
  featured?: boolean
  limit?: number
}) {
  let posts = [...blogPosts]

  if (options?.category && options.category !== '所有分类') {
    posts = posts.filter(post => post.category === options.category)
  }

  if (options?.featured !== undefined) {
    posts = posts.filter(post => post.featured === options.featured)
  }

  if (options?.limit) {
    posts = posts.slice(0, options.limit)
  }

  return posts
}

// 根据 slug 获取博客文章
export function getBlogPostBySlug(slug: string) {
  return blogPosts.find(post => post.slug === slug)
}

// 获取相关文章
export function getRelatedPosts(currentPostId: string, limit = 3) {
  const currentPost = blogPosts.find(post => post.id === currentPostId)
  if (!currentPost) return []

  return blogPosts
    .filter(post =>
      post.id !== currentPostId &&
      (post.category === currentPost.category ||
       post.tags.some(tag => currentPost.tags.includes(tag)))
    )
    .slice(0, limit)
}


