/**
 * localStorage 工具类
 * 提供类型安全的本地存储操作，支持 JSON 序列化、过期时间、命名空间等功能
 */

export interface StorageOptions {
  /** 过期时间（毫秒），不设置则永不过期 */
  expires?: number
  /** 命名空间前缀 */
  namespace?: string
  /** 是否启用压缩（对于大数据） */
  compress?: boolean
}

export interface StorageItem<T = unknown> {
  /** 存储的数据 */
  data: T
  /** 存储时间戳 */
  timestamp: number
  /** 过期时间戳，null 表示永不过期 */
  expires: number | null
  /** 数据版本，用于兼容性检查 */
  version: string
}

export interface StorageConfig {
  /** 默认命名空间 */
  defaultNamespace: string
  /** 默认数据版本 */
  version: string
  /** 是否启用调试模式 */
  debug: boolean
}

/**
 * localStorage 工具类
 */
export class Storage {
  private config: StorageConfig
  private isAvailable: boolean

  constructor(config: Partial<StorageConfig> = {}) {
    this.config = {
      defaultNamespace: 'shadcn_admin',
      version: '1.0.0',
      debug: false,
      ...config,
    }
    
    this.isAvailable = this.checkAvailability()
    
    if (this.config.debug && !this.isAvailable) {
      // eslint-disable-next-line no-console
      console.warn('localStorage is not available, falling back to memory storage')
    }
  }

  /**
   * 检查 localStorage 是否可用
   */
  private checkAvailability(): boolean {
    try {
      const testKey = '__storage_test__'
      localStorage.setItem(testKey, 'test')
      localStorage.removeItem(testKey)
      return true
    } catch {
      return false
    }
  }

  /**
   * 生成带命名空间的键名
   */
  private getKey(key: string, namespace?: string): string {
    const ns = namespace || this.config.defaultNamespace
    return `${ns}:${key}`
  }

  /**
   * 创建存储项
   */
  private createStorageItem<T>(data: T, options: StorageOptions = {}): StorageItem<T> {
    const now = Date.now()
    return {
      data,
      timestamp: now,
      expires: options.expires ? now + options.expires : null,
      version: this.config.version,
    }
  }

  /**
   * 验证存储项是否有效
   */
  private isValidStorageItem<T>(item: unknown): item is StorageItem<T> {
    return !!(
      item &&
      typeof item === 'object' &&
      'data' in item &&
      'timestamp' in item &&
      'expires' in item &&
      'version' in item
    )
  }

  /**
   * 检查存储项是否过期
   */
  private isExpired(item: StorageItem): boolean {
    if (item.expires === null) return false
    return Date.now() > item.expires
  }

  /**
   * 存储数据
   */
  set<T>(key: string, value: T, options: StorageOptions = {}): boolean {
    try {
      const storageKey = this.getKey(key, options.namespace)
      const storageItem = this.createStorageItem(value, options)
      const serializedValue = JSON.stringify(storageItem)

      if (this.isAvailable) {
        localStorage.setItem(storageKey, serializedValue)
      } else {
        // 降级到内存存储（可以扩展为 sessionStorage 或其他方案）
        this.memoryStorage.set(storageKey, serializedValue)
      }

      if (this.config.debug) {
        // eslint-disable-next-line no-console
        console.log(`Storage.set: ${storageKey}`, value)
      }

      return true
    } catch (error) {
      if (this.config.debug) {
        // eslint-disable-next-line no-console
        console.error(`Storage.set failed for key: ${key}`, error)
      }
      return false
    }
  }

  /**
   * 获取数据
   */
  get<T>(key: string, defaultValue?: T, options: StorageOptions = {}): T | undefined {
    try {
      const storageKey = this.getKey(key, options.namespace)
      let serializedValue: string | null

      if (this.isAvailable) {
        serializedValue = localStorage.getItem(storageKey)
      } else {
        serializedValue = this.memoryStorage.get(storageKey) || null
      }

      if (serializedValue === null) {
        return defaultValue
      }

      const parsedValue = JSON.parse(serializedValue)

      if (!this.isValidStorageItem<T>(parsedValue)) {
        if (this.config.debug) {
          console.warn(`Invalid storage item for key: ${key}`)
        }
        this.remove(key, options)
        return defaultValue
      }

      if (this.isExpired(parsedValue)) {
        if (this.config.debug) {
          console.log(`Storage item expired for key: ${key}`)
        }
        this.remove(key, options)
        return defaultValue
      }

      if (this.config.debug) {
        console.log(`Storage.get: ${storageKey}`, parsedValue.data)
      }

      return parsedValue.data
    } catch (error) {
      if (this.config.debug) {
        console.error(`Storage.get failed for key: ${key}`, error)
      }
      return defaultValue
    }
  }

  /**
   * 删除数据
   */
  remove(key: string, options: StorageOptions = {}): boolean {
    try {
      const storageKey = this.getKey(key, options.namespace)

      if (this.isAvailable) {
        localStorage.removeItem(storageKey)
      } else {
        this.memoryStorage.delete(storageKey)
      }

      if (this.config.debug) {
        console.log(`Storage.remove: ${storageKey}`)
      }

      return true
    } catch (error) {
      if (this.config.debug) {
        console.error(`Storage.remove failed for key: ${key}`, error)
      }
      return false
    }
  }

  /**
   * 检查键是否存在
   */
  has(key: string, options: StorageOptions = {}): boolean {
    const value = this.get(key, undefined, options)
    return value !== undefined
  }

  /**
   * 清除所有数据
   */
  clear(namespace?: string): boolean {
    try {
      const ns = namespace || this.config.defaultNamespace
      const prefix = `${ns}:`

      if (this.isAvailable) {
        const keysToRemove: string[] = []
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i)
          if (key && key.startsWith(prefix)) {
            keysToRemove.push(key)
          }
        }
        keysToRemove.forEach(key => localStorage.removeItem(key))
      } else {
        // 清除内存存储中的相关数据
        for (const key of this.memoryStorage.keys()) {
          if (key.startsWith(prefix)) {
            this.memoryStorage.delete(key)
          }
        }
      }

      if (this.config.debug) {
        console.log(`Storage.clear: namespace ${ns}`)
      }

      return true
    } catch (error) {
      if (this.config.debug) {
        console.error(`Storage.clear failed for namespace: ${namespace}`, error)
      }
      return false
    }
  }

  /**
   * 获取所有键
   */
  keys(namespace?: string): string[] {
    try {
      const ns = namespace || this.config.defaultNamespace
      const prefix = `${ns}:`
      const keys: string[] = []

      if (this.isAvailable) {
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i)
          if (key && key.startsWith(prefix)) {
            keys.push(key.substring(prefix.length))
          }
        }
      } else {
        for (const key of this.memoryStorage.keys()) {
          if (key.startsWith(prefix)) {
            keys.push(key.substring(prefix.length))
          }
        }
      }

      return keys
    } catch (error) {
      if (this.config.debug) {
        console.error(`Storage.keys failed for namespace: ${namespace}`, error)
      }
      return []
    }
  }

  /**
   * 获取存储大小（字节）
   */
  getSize(namespace?: string): number {
    try {
      const ns = namespace || this.config.defaultNamespace
      const prefix = `${ns}:`
      let size = 0

      if (this.isAvailable) {
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i)
          if (key && key.startsWith(prefix)) {
            const value = localStorage.getItem(key)
            if (value) {
              size += key.length + value.length
            }
          }
        }
      } else {
        for (const [key, value] of this.memoryStorage.entries()) {
          if (key.startsWith(prefix)) {
            size += key.length + value.length
          }
        }
      }

      return size
    } catch (error) {
      if (this.config.debug) {
        console.error(`Storage.getSize failed for namespace: ${namespace}`, error)
      }
      return 0
    }
  }

  /**
   * 清理过期数据
   */
  cleanup(namespace?: string): number {
    try {
      const ns = namespace || this.config.defaultNamespace
      const prefix = `${ns}:`
      let cleanedCount = 0

      if (this.isAvailable) {
        const keysToRemove: string[] = []
        
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i)
          if (key && key.startsWith(prefix)) {
            const value = localStorage.getItem(key)
            if (value) {
              try {
                const parsedValue = JSON.parse(value)
                if (this.isValidStorageItem(parsedValue) && this.isExpired(parsedValue)) {
                  keysToRemove.push(key)
                }
              } catch {
                // 无效的 JSON，也删除
                keysToRemove.push(key)
              }
            }
          }
        }

        keysToRemove.forEach(key => {
          localStorage.removeItem(key)
          cleanedCount++
        })
      }

      if (this.config.debug) {
        console.log(`Storage.cleanup: removed ${cleanedCount} expired items`)
      }

      return cleanedCount
    } catch (error) {
      if (this.config.debug) {
        console.error(`Storage.cleanup failed for namespace: ${namespace}`, error)
      }
      return 0
    }
  }

  /**
   * 内存存储降级方案
   */
  private memoryStorage = new Map<string, string>()

  /**
   * 获取存储统计信息
   */
  getStats(namespace?: string) {
    const ns = namespace || this.config.defaultNamespace
    const keys = this.keys(ns)
    const size = this.getSize(ns)
    
    return {
      namespace: ns,
      keyCount: keys.length,
      totalSize: size,
      isAvailable: this.isAvailable,
      version: this.config.version,
    }
  }
}

// 创建默认实例
export const storage = new Storage({
  defaultNamespace: 'shadcn_admin',
  version: '1.0.0',
  debug: process.env.NODE_ENV === 'development',
})

