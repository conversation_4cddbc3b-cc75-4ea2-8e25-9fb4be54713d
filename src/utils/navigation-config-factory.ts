import { TFunction } from 'i18next'
import { CustomNavExample } from '@/components/layout/custom-nav-example'
import {
  NavigationComponentType,
  type NavigationConfig,
  type TopNavLink,
  type BreadcrumbLink,
  type TabNavLink,
} from '@/types/navigation'

/**
 * 导航配置工厂函数
 * 提供预定义的导航配置，便于复用和维护
 */
export class NavigationConfigFactory {
  constructor(private t: TFunction) {}

  /**
   * 创建 Dashboard TopNav 配置
   */
  createDashboardTopNav(currentPath: string): NavigationConfig {
    const links: TopNavLink[] = [
      {
        title: this.t('dashboard.overview'),
        href: '/dashboard/overview',
        isActive: this.isPathActive(currentPath, '/dashboard/overview'),
        disabled: false,
      },
      {
        title: this.t('dashboard.customers'),
        href: '/dashboard/customers',
        isActive: this.isPathActive(currentPath, '/dashboard/customers'),
        disabled: true,
      },
      {
        title: this.t('dashboard.products'),
        href: '/dashboard/products',
        isActive: this.isPathActive(currentPath, '/dashboard/products'),
        disabled: true,
      },
      {
        title: this.t('navigation.settings'),
        href: '/dashboard/settings',
        isActive: this.isPathActive(currentPath, '/dashboard/settings'),
        disabled: true,
      },
    ]

    return {
      type: NavigationComponentType.TOP_NAV,
      props: { links }
    }
  }

  /**
   * 路径匹配工具函数
   * 检查当前路径是否与目标路径匹配
   */
  private isPathActive(currentPath: string, targetPath: string): boolean {
    // 精确匹配
    if (currentPath === targetPath) {
      return true
    }

    // 去除查询参数后匹配
    const currentPathWithoutQuery = currentPath.split('?')[0]
    const targetPathWithoutQuery = targetPath.split('?')[0]

    if (currentPathWithoutQuery === targetPathWithoutQuery) {
      return true
    }

    // 对于 dashboard overview，如果当前路径是 /dashboard，也认为是激活状态
    if (targetPath === '/dashboard/overview' && currentPath === '/dashboard') {
      return true
    }

    return false
  }

  /**
   * 创建设置页面面包屑配置
   */
  createSettingsBreadcrumb(currentPath: string): NavigationConfig {
    const links: BreadcrumbLink[] = [
      { title: this.t('navigation.settings'), href: '/dashboard/settings' },
    ]

    // 根据当前路径添加子页面
    const pathMap: Record<string, string> = {
      '/dashboard/settings/account': 'navigation.account',
      '/dashboard/settings/appearance': 'navigation.appearance',
      '/dashboard/settings/notifications': 'navigation.notifications',
      '/dashboard/settings/display': 'navigation.display',
    }

    if (pathMap[currentPath]) {
      links.push({ title: this.t(pathMap[currentPath]) })
    }

    return {
      type: NavigationComponentType.BREADCRUMB,
      props: { links }
    }
  }

  /**
   * 创建任务页面 TabNav 配置
   */
  createTasksTabNav(currentPath: string): NavigationConfig {
    const links: TabNavLink[] = [
      {
        title: this.t('tasks.title'),
        href: '/dashboard/tasks',
        isActive: this.isPathActive(currentPath, '/dashboard/tasks'),
        disabled: false,
      },
      {
        title: this.t('common.all'),
        href: '/dashboard/tasks?filter=all',
        isActive: currentPath.includes('filter=all'),
        disabled: false,
        badge: '12',
      },
      {
        title: this.t('tasks.inProgress'),
        href: '/dashboard/tasks?filter=progress',
        isActive: currentPath.includes('filter=progress'),
        disabled: false,
        badge: '5',
      },
      {
        title: this.t('tasks.done'),
        href: '/dashboard/tasks?filter=done',
        isActive: currentPath.includes('filter=done'),
        disabled: false,
        badge: '7',
      },
    ]

    return {
      type: NavigationComponentType.TAB_NAV,
      props: {
        links,
        variant: 'underline' as const
      }
    }
  }

  /**
   * 创建自定义导航配置示例
   */
  createCustomNavExample(): NavigationConfig {
    const items = [
      {
        title: this.t('navigation.dashboard'),
        href: '/',
        isActive: true,
      },
      {
        title: this.t('navigation.users'),
        children: [
          { title: this.t('users.title'), href: '/users' },
          { title: this.t('navigation.userManagement'), href: '/users/management' },
        ],
      },
      {
        title: this.t('navigation.settings'),
        children: [
          { title: this.t('navigation.profile'), href: '/settings' },
          { title: this.t('navigation.account'), href: '/settings/account' },
          { title: this.t('navigation.appearance'), href: '/settings/appearance' },
        ],
      },
    ]

    return {
      type: NavigationComponentType.CUSTOM,
      config: {
        component: CustomNavExample,
        props: {
          items,
          showSearch: true,
          variant: 'horizontal',
        }
      }
    }
  }

  /**
   * 创建空导航配置
   */
  createEmptyNav(): NavigationConfig {
    return {
      type: NavigationComponentType.NONE
    }
  }
}

/**
 * 便捷函数：根据路径和翻译函数创建导航配置
 */
export function createNavigationConfig(
  pathname: string,
  t: TFunction
): NavigationConfig {
  const factory = new NavigationConfigFactory(t)

  // 优先匹配更具体的路径

  // Settings 页面 - 必须在 dashboard 之前匹配
  if (pathname.startsWith('/dashboard/settings')) {
    return factory.createSettingsBreadcrumb(pathname)
  }

  // Tasks 页面 - 必须在 dashboard 之前匹配
  if (pathname.startsWith('/dashboard/tasks')) {
    return factory.createTasksTabNav(pathname)
  }

  // Dashboard 页面 - 通用匹配
  if (pathname.startsWith('/dashboard/overview')) {
    return factory.createDashboardTopNav(pathname)
  }

  // 其他页面不显示导航
  return factory.createEmptyNav()
}
