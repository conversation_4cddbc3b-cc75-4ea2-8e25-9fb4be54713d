import { toast } from 'sonner'
import i18n from '@/lib/i18n'

export function showSubmittedData(
  data: unknown,
  title?: string
) {
  const defaultTitle = title || i18n.t('messages.dataSubmitted')
  toast.message(defaultTitle, {
    description: (
      // w-[340px]
      <pre className='mt-2 w-full overflow-x-auto rounded-md bg-slate-950 p-4'>
        <code className='text-white'>{JSON.stringify(data, null, 2)}</code>
      </pre>
    ),
  })
}
