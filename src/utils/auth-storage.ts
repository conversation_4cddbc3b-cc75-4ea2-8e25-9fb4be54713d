/**
 * 认证相关的存储工具
 * 基于通用 Storage 类，提供认证数据的专门存储方法
 */

import { storage, type StorageOptions } from './storage'

// 认证相关的存储键
export const AUTH_STORAGE_KEYS = {
  ACCESS_TOKEN: 'access_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_DATA: 'user_data',
  AUTH_STATE: 'auth_state',
  LOGIN_TIMESTAMP: 'login_timestamp',
  LAST_ACTIVITY: 'last_activity',
} as const

// 认证用户数据类型
export interface AuthUser {
  id: string
  accountNo: string
  email: string
  firstName: string
  lastName: string
  username: string
  avatar?: string
  role: string[]
  exp: number
  createdAt?: string
  updatedAt?: string
}

// 认证状态数据类型
export interface AuthState {
  isAuthenticated: boolean
  loginTimestamp?: number
  lastActivity?: number
}

// 认证存储配置
const AUTH_STORAGE_OPTIONS: StorageOptions = {
  namespace: 'auth',
  // 认证数据 24 小时过期
  expires: 24 * 60 * 60 * 1000,
}

// Token 存储配置（更短的过期时间）
const TOKEN_STORAGE_OPTIONS: StorageOptions = {
  namespace: 'auth',
  // Token 12 小时过期
  expires: 12 * 60 * 60 * 1000,
}

/**
 * 认证存储工具类
 */
export class AuthStorage {
  /**
   * 存储访问令牌
   */
  static setAccessToken(token: string): boolean {
    return storage.set(AUTH_STORAGE_KEYS.ACCESS_TOKEN, token, TOKEN_STORAGE_OPTIONS)
  }

  /**
   * 获取访问令牌
   */
  static getAccessToken(): string | undefined {
    return storage.get<string>(AUTH_STORAGE_KEYS.ACCESS_TOKEN, undefined, TOKEN_STORAGE_OPTIONS)
  }

  /**
   * 删除访问令牌
   */
  static removeAccessToken(): boolean {
    return storage.remove(AUTH_STORAGE_KEYS.ACCESS_TOKEN, TOKEN_STORAGE_OPTIONS)
  }

  /**
   * 存储刷新令牌
   */
  static setRefreshToken(token: string): boolean {
    return storage.set(AUTH_STORAGE_KEYS.REFRESH_TOKEN, token, {
      namespace: 'auth',
      // 刷新令牌 30 天过期
      expires: 30 * 24 * 60 * 60 * 1000,
    })
  }

  /**
   * 获取刷新令牌
   */
  static getRefreshToken(): string | undefined {
    return storage.get<string>(AUTH_STORAGE_KEYS.REFRESH_TOKEN, undefined, {
      namespace: 'auth',
    })
  }

  /**
   * 删除刷新令牌
   */
  static removeRefreshToken(): boolean {
    return storage.remove(AUTH_STORAGE_KEYS.REFRESH_TOKEN, { namespace: 'auth' })
  }

  /**
   * 存储用户数据
   */
  static setUserData(user: AuthUser): boolean {
    return storage.set(AUTH_STORAGE_KEYS.USER_DATA, user, AUTH_STORAGE_OPTIONS)
  }

  /**
   * 获取用户数据
   */
  static getUserData(): AuthUser | undefined {
    return storage.get<AuthUser>(AUTH_STORAGE_KEYS.USER_DATA, undefined, AUTH_STORAGE_OPTIONS)
  }

  /**
   * 删除用户数据
   */
  static removeUserData(): boolean {
    return storage.remove(AUTH_STORAGE_KEYS.USER_DATA, AUTH_STORAGE_OPTIONS)
  }

  /**
   * 存储认证状态
   */
  static setAuthState(authState: AuthState): boolean {
    return storage.set(AUTH_STORAGE_KEYS.AUTH_STATE, authState, AUTH_STORAGE_OPTIONS)
  }

  /**
   * 获取认证状态
   */
  static getAuthState(): AuthState | undefined {
    return storage.get<AuthState>(AUTH_STORAGE_KEYS.AUTH_STATE, undefined, AUTH_STORAGE_OPTIONS)
  }

  /**
   * 删除认证状态
   */
  static removeAuthState(): boolean {
    return storage.remove(AUTH_STORAGE_KEYS.AUTH_STATE, AUTH_STORAGE_OPTIONS)
  }

  /**
   * 更新最后活动时间
   */
  static updateLastActivity(): boolean {
    const currentState = this.getAuthState()
    if (currentState) {
      return this.setAuthState({
        ...currentState,
        lastActivity: Date.now(),
      })
    }
    return false
  }

  /**
   * 检查认证数据是否存在且有效
   */
  static isAuthDataValid(): boolean {
    const token = this.getAccessToken()
    const user = this.getUserData()
    const authState = this.getAuthState()

    if (!token || !user || !authState) {
      return false
    }

    // 检查 token 是否过期
    if (user.exp && Date.now() >= user.exp) {
      return false
    }

    return authState.isAuthenticated
  }

  /**
   * 获取完整的认证信息
   */
  static getAuthInfo() {
    return {
      accessToken: this.getAccessToken(),
      refreshToken: this.getRefreshToken(),
      user: this.getUserData(),
      authState: this.getAuthState(),
      isValid: this.isAuthDataValid(),
    }
  }

  /**
   * 存储完整的登录信息
   */
  static setLoginInfo(user: AuthUser, accessToken: string, refreshToken?: string): boolean {
    const loginTimestamp = Date.now()

    // 更新用户过期时间
    const userWithExpiry = {
      ...user,
      exp: user.exp || loginTimestamp + 24 * 60 * 60 * 1000, // 默认 24 小时
    }

    const authState: AuthState = {
      isAuthenticated: true,
      loginTimestamp,
      lastActivity: loginTimestamp,
    }

    // 批量存储
    const results = [
      this.setAccessToken(accessToken),
      this.setUserData(userWithExpiry),
      this.setAuthState(authState),
    ]

    if (refreshToken) {
      results.push(this.setRefreshToken(refreshToken))
    }

    return results.every(result => result)
  }

  /**
   * 清除所有认证数据
   */
  static clearAll(): boolean {
    const results = [
      this.removeAccessToken(),
      this.removeRefreshToken(),
      this.removeUserData(),
      this.removeAuthState(),
    ]

    return results.every(result => result)
  }

  /**
   * 获取认证存储统计信息
   */
  static getStats() {
    return storage.getStats('auth')
  }

  /**
   * 清理过期的认证数据
   */
  static cleanup(): number {
    return storage.cleanup('auth')
  }

  /**
   * 检查是否需要刷新 token
   */
  static shouldRefreshToken(): boolean {
    const user = this.getUserData()
    if (!user || !user.exp) return false

    // 如果 token 在 1 小时内过期，则需要刷新
    const oneHour = 60 * 60 * 1000
    return (user.exp - Date.now()) < oneHour
  }

  /**
   * 获取 token 剩余有效时间（毫秒）
   */
  static getTokenRemainingTime(): number {
    const user = this.getUserData()
    if (!user || !user.exp) return 0

    const remaining = user.exp - Date.now()
    return Math.max(0, remaining)
  }

  /**
   * 检查用户是否有特定权限
   */
  static hasPermission(requiredRoles: string[]): boolean {
    const user = this.getUserData()
    if (!user || !user.role) return false

    return requiredRoles.some(role => user.role.includes(role))
  }

  /**
   * 检查用户是否为管理员
   */
  static isAdmin(): boolean {
    return this.hasPermission(['admin', 'superadmin'])
  }

  /**
   * 检查用户是否为超级管理员
   */
  static isSuperAdmin(): boolean {
    return this.hasPermission(['superadmin'])
  }

  /**
   * 获取用户显示名称
   */
  static getUserDisplayName(): string {
    const user = this.getUserData()
    if (!user) return 'Guest'

    if (user.firstName && user.lastName) {
      return `${user.firstName} ${user.lastName}`
    }
    if (user.username) {
      return user.username
    }
    return user.email.split('@')[0]
  }

  /**
   * 获取用户头像缩写
   */
  static getUserInitials(): string {
    const user = this.getUserData()
    if (!user) return 'G'

    if (user.firstName && user.lastName) {
      return `${user.firstName[0]}${user.lastName[0]}`.toUpperCase()
    }
    if (user.username) {
      return user.username.slice(0, 2).toUpperCase()
    }
    return user.email.slice(0, 2).toUpperCase()
  }
}

// 导出默认实例
export { AuthStorage as default }
