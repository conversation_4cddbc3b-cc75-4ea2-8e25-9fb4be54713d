import { Link } from '@tanstack/react-router'
import { useTranslation } from 'react-i18next'
import { IconArrowRight, IconCheck, IconStar } from '@tabler/icons-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

export default function LandingPage() {
  const { t } = useTranslation()

  const features = [
    {
      title: '现代化设计',
      description: '基于 Shadcn UI 的美观组件库',
      icon: '🎨',
    },
    {
      title: '完全响应式',
      description: '适配所有设备和屏幕尺寸',
      icon: '📱',
    },
    {
      title: '类型安全',
      description: '完整的 TypeScript 支持',
      icon: '🛡️',
    },
    {
      title: '国际化支持',
      description: '多语言界面，支持全球化',
      icon: '🌍',
    },
    {
      title: '暗色模式',
      description: '内置明暗主题切换',
      icon: '🌙',
    },
    {
      title: '开源免费',
      description: '完全开源，可自由定制',
      icon: '💝',
    },
  ]

  const testimonials = [
    {
      name: '张三',
      role: '前端开发工程师',
      company: 'TechCorp',
      content: '这个管理后台模板帮我们节省了大量开发时间，组件质量很高。',
      rating: 5,
    },
    {
      name: 'John Smith',
      role: 'Product Manager',
      company: 'StartupXYZ',
      content: 'Amazing dashboard template with great UX. Highly recommended!',
      rating: 5,
    },
    {
      name: '李四',
      role: 'UI/UX 设计师',
      company: 'DesignStudio',
      content: '设计精美，交互流畅，是我见过最好的管理后台模板之一。',
      rating: 5,
    },
  ]

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="container py-24 md:py-32">
        <div className="mx-auto max-w-4xl text-center">
          <Badge variant="outline" className="mb-4">
            🎉 现已开源
          </Badge>
          <h1 className="text-4xl font-bold tracking-tight sm:text-6xl mb-6">
            {t('marketing.heroTitle')}
          </h1>
          <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
            {t('marketing.heroSubtitle')}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" asChild>
              <Link to="/dashboard">
                {t('marketing.viewDemo')}
                <IconArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
            <Button size="lg" variant="outline" asChild>
              <Link to="/features">
                {t('marketing.learnMore')}
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="container py-24 bg-muted/50">
        <div className="mx-auto max-w-4xl text-center mb-16">
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
            强大的功能特性
          </h2>
          <p className="text-xl text-muted-foreground">
            为现代化管理后台提供完整的解决方案
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <Card key={index} className="text-center">
              <CardHeader>
                <div className="text-4xl mb-4">{feature.icon}</div>
                <CardTitle>{feature.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>{feature.description}</CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="container py-24">
        <div className="mx-auto max-w-4xl text-center mb-16">
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
            用户评价
          </h2>
          <p className="text-xl text-muted-foreground">
            看看其他开发者怎么说
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <Card key={index}>
              <CardHeader>
                <div className="flex items-center gap-1 mb-2">
                  {Array.from({ length: testimonial.rating }).map((_, i) => (
                    <IconStar key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
                <CardDescription>"{testimonial.content}"</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="font-semibold">{testimonial.name}</div>
                <div className="text-sm text-muted-foreground">
                  {testimonial.role} @ {testimonial.company}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* CTA Section */}
      <section className="container py-24 bg-muted/50">
        <div className="mx-auto max-w-4xl text-center">
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
            准备开始了吗？
          </h2>
          <p className="text-xl text-muted-foreground mb-8">
            立即开始构建您的管理后台，完全免费
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" asChild>
              <Link to="/sign-up">
                {t('marketing.getStarted')}
                <IconArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
            <Button size="lg" variant="outline" asChild>
              <Link to="/pricing">
                查看定价
              </Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  )
}
