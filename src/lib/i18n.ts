import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'

// Import translation files
import zhCN from '@/locales/zh-CN.json'
import enUS from '@/locales/en-US.json'

const resources = {
  'zh-CN': {
    translation: zhCN,
  },
  'en-US': {
    translation: enUS,
  },
}

i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: localStorage.getItem('language') || 'zh-CN', // 默认中文简体
    fallbackLng: 'en-US', // 备选语言为英文
    
    interpolation: {
      escapeValue: false, // React 已经默认转义
    },
    
    // 调试模式（仅在开发环境）
    debug: import.meta.env.DEV,
    
    // 命名空间配置
    defaultNS: 'translation',
    ns: ['translation'],
    
    // 检测配置
    detection: {
      order: ['localStorage', 'navigator'],
      caches: ['localStorage'],
    },
  })

export default i18n
