/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * API 类型定义
 * 统一的 API 响应格式和错误类型定义
 */

// 基础 API 响应格式
export interface ApiResponse<T = any> {
  success: boolean
  data: T
  message?: string
  code?: string | number
  timestamp?: string
  requestId?: string
}

// 分页响应格式
export interface PaginatedResponse<T = any> {
  success: boolean
  data: {
    items: T[]
    total: number
    page: number
    pageSize: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
  message?: string
  code?: string | number
}

// 错误响应格式
export interface ApiError {
  success: false
  message: string
  code: string | number
  details?: Record<string, any>
  timestamp?: string
  requestId?: string
  stack?: string // 仅开发环境
}

// HTTP 错误类型
export interface HttpError extends Error {
  status: number
  statusText: string
  response?: any
  config?: any
}

// 业务错误类型
export interface BusinessError extends Error {
  code: string | number
  details?: Record<string, any>
  requestId?: string
}

// 网络错误类型
export interface NetworkError extends Error {
  code: 'NETWORK_ERROR' | 'TIMEOUT' | 'CANCELLED'
  timeout?: boolean
  cancelled?: boolean
}

// 请求配置扩展
export interface ApiRequestConfig {
  // 是否显示 loading
  showLoading?: boolean
  // 是否显示错误提示
  showError?: boolean
  // 是否显示成功提示
  showSuccess?: boolean
  // 成功提示消息
  successMessage?: string
  // 加载提示消息
  loadingMessage?: string
  // 自定义错误处理
  customErrorHandler?: (error: unknown) => void
  // 是否需要认证
  requireAuth?: boolean
  // 是否重试
  retry?: boolean | number
  // 重试延迟
  retryDelay?: number
  // 是否去重
  dedupe?: boolean
  // 缓存时间（毫秒）
  cacheTime?: number
  // 是否加密请求数据
  encrypt?: boolean
  // CSRF token
  csrfToken?: string
  // 是否启用请求签名
  requestSigning?: boolean
}

// 扩展的 Axios 请求配置
export interface ExtendedAxiosRequestConfig extends ApiRequestConfig {
  metadata?: {
    startTime: number
  }
  __retryCount?: number
  __dedupeResolve?: (value: any) => void
  __dedupeReject?: (reason?: any) => void
}

// 文件上传配置
export interface UploadConfig extends ApiRequestConfig {
  // 上传进度回调
  onProgress?: (progress: number) => void
  // 支持的文件类型
  accept?: string[]
  // 最大文件大小（字节）
  maxSize?: number
  // 是否支持多文件
  multiple?: boolean
}

// 文件下载配置
export interface DownloadConfig extends ApiRequestConfig {
  // 下载进度回调
  onProgress?: (progress: number) => void
  // 文件名
  filename?: string
  // 是否在新窗口打开
  openInNewTab?: boolean
}

// API 端点配置
export interface ApiEndpoint {
  url: string
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  config?: ApiRequestConfig
}

// 认证相关类型
export interface AuthTokens {
  accessToken: string
  refreshToken?: string
  tokenType?: string
  expiresIn?: number
  expiresAt?: number
}

export interface RefreshTokenResponse {
  accessToken: string
  refreshToken?: string
  expiresIn?: number
}

// 请求拦截器类型
export interface RequestInterceptor {
  onFulfilled?: (config: any) => any | Promise<any>
  onRejected?: (error: any) => any
}

// 响应拦截器类型
export interface ResponseInterceptor {
  onFulfilled?: (response: any) => any | Promise<any>
  onRejected?: (error: any) => any
}

// API 客户端配置
export interface ApiClientConfig {
  baseURL: string
  timeout: number
  headers?: Record<string, string>
  withCredentials?: boolean
  // 请求拦截器
  requestInterceptors?: RequestInterceptor[]
  // 响应拦截器
  responseInterceptors?: ResponseInterceptor[]
  // 是否启用请求日志
  enableLogging?: boolean
  // 是否启用错误重试
  enableRetry?: boolean
  // 默认重试次数
  defaultRetryCount?: number
  // 重试延迟
  retryDelay?: number
  // 是否启用请求去重
  enableDedupe?: boolean
  // 去重缓存时间
  dedupeTime?: number
}

// 环境配置
export interface EnvironmentConfig {
  development: {
    baseURL: string
    timeout: number
    enableLogging: boolean
  }
  production: {
    baseURL: string
    timeout: number
    enableLogging: boolean
  }
  test: {
    baseURL: string
    timeout: number
    enableLogging: boolean
  }
}

// Mock 配置
export interface MockConfig {
  enabled: boolean
  delay?: number
  errorRate?: number // 错误率 0-1
  endpoints?: Record<string, any>
}

// 请求统计
export interface RequestStats {
  total: number
  success: number
  error: number
  pending: number
  averageTime: number
  lastRequestTime?: number
}

// 缓存项
export interface CacheItem<T = any> {
  data: T
  timestamp: number
  expiresAt: number
  key: string
}

// 加密配置
export interface EncryptionConfig {
  enabled: boolean
  algorithm: string
  key: string
  iv?: string
}

// 安全配置
export interface SecurityConfig {
  // XSS 防护
  xssProtection: boolean
  // CSRF 防护
  csrfProtection: boolean
  // 请求签名
  requestSigning: boolean
  // 数据加密
  encryption: EncryptionConfig
}

// 监控配置
export interface MonitoringConfig {
  // 是否启用性能监控
  performance: boolean
  // 是否启用错误监控
  errorTracking: boolean
  // 是否启用用户行为监控
  userTracking: boolean
  // 采样率
  sampleRate: number
}

// 完整的 API 配置
export interface ApiConfig {
  client: ApiClientConfig
  environment: EnvironmentConfig
  security: SecurityConfig
  monitoring: MonitoringConfig
  mock: MockConfig
}

// React Query 相关类型
export interface QueryConfig {
  staleTime?: number
  cacheTime?: number
  refetchOnWindowFocus?: boolean
  refetchOnReconnect?: boolean
  retry?: boolean | number
  retryDelay?: number
}

export interface MutationConfig {
  retry?: boolean | number
  retryDelay?: number
  onSuccess?: (data: any) => void
  onError?: (error: any) => void
  onSettled?: () => void
}

// 导出所有类型的联合类型，便于使用
export type ApiErrorType = HttpError | BusinessError | NetworkError
export type ApiConfigType = ApiRequestConfig | UploadConfig | DownloadConfig
