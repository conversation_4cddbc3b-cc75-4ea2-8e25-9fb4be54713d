/**
 * 统一错误处理模块
 * 处理各种类型的 API 错误并提供用户友好的提示
 */

import { toast } from 'sonner'
import type {
  ApiError,
  HttpError,
  BusinessError,
  NetworkError,
  ApiErrorType,
  ApiRequestConfig
} from './types'
import { ERROR_CODES, ERROR_MESSAGES } from './config'
import { classifyError, generateRequestId } from './utils'

// 获取翻译函数的辅助函数
function getTranslation(key: string, fallback?: string): string {
  // 尝试从全局 i18next 实例获取翻译
  try {
    // @ts-ignore
    if (window.i18n && typeof window.i18n.t === 'function') {
      // @ts-ignore
      return window.i18n.t(key, fallback)
    }
  } catch {
    // 忽略错误
  }

  // 如果没有翻译，返回 fallback 或 key
  return fallback || key
}

// 错误处理器类
export class ErrorHandler {
  private static instance: ErrorHandler

  private constructor() {
    // 不在构造函数中使用 hooks
  }

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler()
    }
    return ErrorHandler.instance
  }

  // 主要错误处理方法
  handle(error: any, config?: ApiRequestConfig): ApiErrorType {
    const errorType = classifyError(error)
    const processedError = this.processError(error, errorType)

    // 记录错误
    this.logError(processedError, config)

    // 显示错误提示
    if (config?.showError !== false) {
      this.showErrorToast(processedError, config)
    }

    // 执行自定义错误处理
    if (config?.customErrorHandler) {
      config.customErrorHandler(processedError)
    }

    return processedError
  }

  // 处理不同类型的错误
  private processError(error: any, type: string): ApiErrorType {
    switch (type) {
      case 'network':
        return this.createNetworkError(error)
      case 'http':
        return this.createHttpError(error)
      case 'business':
        return this.createBusinessError(error)
      default:
        return this.createUnknownError(error)
    }
  }

  // 创建网络错误
  private createNetworkError(error: any): NetworkError {
    const networkError = new Error() as NetworkError
    networkError.name = 'NetworkError'

    if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
      networkError.code = 'TIMEOUT'
      networkError.message = getTranslation('api.errors.timeout', 'Request timeout')
      networkError.timeout = true
    } else if (error.code === 'ERR_CANCELED') {
      networkError.code = 'CANCELLED'
      networkError.message = getTranslation('api.errors.cancelled', 'Request cancelled')
      networkError.cancelled = true
    } else {
      networkError.code = 'NETWORK_ERROR'
      networkError.message = getTranslation('api.errors.networkError', 'Network connection failed')
    }

    return networkError
  }

  // 创建 HTTP 错误
  private createHttpError(error: any): HttpError {
    const httpError = new Error() as HttpError
    httpError.name = 'HttpError'
    httpError.status = error.response?.status || 0
    httpError.statusText = error.response?.statusText || 'Unknown'
    httpError.response = error.response
    httpError.config = error.config

    // 根据状态码设置错误消息
    const messageKey = ERROR_MESSAGES[httpError.status as keyof typeof ERROR_MESSAGES]
    httpError.message = messageKey ? getTranslation(messageKey) : this.getDefaultHttpErrorMessage(httpError.status)

    return httpError
  }

  // 创建业务错误
  private createBusinessError(error: any): BusinessError {
    const businessError = new Error() as BusinessError
    businessError.name = 'BusinessError'
    businessError.code = error.code || ERROR_CODES.BUSINESS_ERROR
    businessError.message = error.message || getTranslation('api.errors.businessError', 'Business logic error')
    businessError.details = error.details
    businessError.requestId = error.requestId

    return businessError
  }

  // 创建未知错误
  private createUnknownError(error: any): HttpError {
    const unknownError = new Error() as HttpError
    unknownError.name = 'UnknownError'
    unknownError.status = 0
    unknownError.statusText = 'Unknown'
    unknownError.message = error.message || getTranslation('api.errors.unknownError', 'Unknown error occurred')

    return unknownError
  }

  // 获取默认 HTTP 错误消息
  private getDefaultHttpErrorMessage(status: number): string {
    if (status >= 500) {
      return getTranslation('api.errors.serverError', 'Server error')
    } else if (status >= 400) {
      return getTranslation('api.errors.clientError', 'Client error')
    } else {
      return getTranslation('api.errors.unknownError', 'Unknown error')
    }
  }

  // 显示错误提示
  private showErrorToast(error: ApiErrorType, config?: ApiRequestConfig): void {
    const errorType = classifyError(error)

    // 根据错误类型选择不同的提示样式
    switch (errorType) {
      case 'network':
        toast.error(error.message, {
          description: getTranslation('api.errors.networkErrorDesc', 'Please check your network connection'),
          action: {
            label: getTranslation('common.retry', 'Retry'),
            onClick: () => {
              // 这里可以触发重试逻辑
              // eslint-disable-next-line no-console
              console.log('Retry requested')
            }
          }
        })
        break

      case 'http':
        const httpError = error as HttpError
        if (httpError.status === 401) {
          toast.error(error.message, {
            description: getTranslation('api.errors.unauthorizedDesc', 'Please login again'),
            action: {
              label: getTranslation('auth.login', 'Login'),
              onClick: () => {
                // 跳转到登录页面
                window.location.href = '/sign-in'
              }
            }
          })
        } else if (httpError.status === 403) {
          toast.error(error.message, {
            description: getTranslation('api.errors.forbiddenDesc', 'You do not have permission to perform this action')
          })
        } else if (httpError.status >= 500) {
          toast.error(error.message, {
            description: getTranslation('api.errors.serverErrorDesc', 'Server is temporarily unavailable')
          })
        } else {
          toast.error(error.message)
        }
        break

      case 'business':
        const businessError = error as BusinessError
        toast.warning(error.message, {
          description: businessError.details ? JSON.stringify(businessError.details) : undefined
        })
        break

      default:
        toast.error(error.message)
        break
    }
  }

  // 记录错误日志
  private logError(error: ApiErrorType, config?: ApiRequestConfig): void {
    const errorInfo = {
      timestamp: new Date().toISOString(),
      requestId: generateRequestId(),
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
        ...('status' in error && { status: error.status }),
        ...('code' in error && { code: error.code }),
      },
      config: config ? {
        url: (config as any).url,
        method: (config as any).method,
        headers: (config as any).headers,
      } : undefined,
      userAgent: navigator.userAgent,
      url: window.location.href,
    }

    // 开发环境下在控制台输出详细错误信息
    if (import.meta.env.DEV) {
      // eslint-disable-next-line no-console
      console.group('🚨 API Error')
      // eslint-disable-next-line no-console
      console.error('Error:', error)
      // eslint-disable-next-line no-console
      console.table(errorInfo)
      // eslint-disable-next-line no-console
      console.groupEnd()
    }

    // 生产环境下发送错误到监控服务
    if (import.meta.env.PROD) {
      this.sendErrorToMonitoring(errorInfo)
    }
  }

  // 发送错误到监控服务
  private sendErrorToMonitoring(errorInfo: any): void {
    try {
      // 这里可以集成第三方错误监控服务，如 Sentry、LogRocket 等
      // 示例：
      // Sentry.captureException(errorInfo)

      // 或者发送到自己的错误收集接口
      fetch('/api/errors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(errorInfo),
      }).catch(() => {
        // 忽略错误上报失败
      })
    } catch {
      // 忽略监控服务错误
    }
  }
}

// 创建全局错误处理器实例
export const errorHandler = ErrorHandler.getInstance()

// 便捷的错误处理函数
export const handleApiError = (error: any, config?: ApiRequestConfig): ApiErrorType => {
  return errorHandler.handle(error, config)
}

// 特定错误处理函数
export const handleNetworkError = (error: any): NetworkError => {
  return errorHandler.handle(error) as NetworkError
}

export const handleHttpError = (error: any): HttpError => {
  return errorHandler.handle(error) as HttpError
}

export const handleBusinessError = (error: any): BusinessError => {
  return errorHandler.handle(error) as BusinessError
}

// 错误边界组件的错误处理
export const handleComponentError = (error: Error, errorInfo: any): void => {
  const errorData = {
    timestamp: new Date().toISOString(),
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
    },
    errorInfo,
    url: window.location.href,
    userAgent: navigator.userAgent,
  }

  // 开发环境输出错误
  if (import.meta.env.DEV) {
    // eslint-disable-next-line no-console
    console.group('🚨 Component Error')
    // eslint-disable-next-line no-console
    console.error('Error:', error)
    // eslint-disable-next-line no-console
    console.error('Error Info:', errorInfo)
    // eslint-disable-next-line no-console
    console.groupEnd()
  }

  // 显示用户友好的错误提示
  toast.error('页面出现错误', {
    description: '请刷新页面重试，如果问题持续存在请联系技术支持',
    action: {
      label: '刷新页面',
      onClick: () => window.location.reload()
    }
  })

  // 发送到监控服务
  if (import.meta.env.PROD) {
    try {
      fetch('/api/errors/component', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(errorData),
      }).catch(() => {
        // 忽略错误上报失败
      })
    } catch {
      // 忽略监控服务错误
    }
  }
}

// 全局未捕获错误处理
export const setupGlobalErrorHandling = (): void => {
  // 处理未捕获的 Promise 错误
  window.addEventListener('unhandledrejection', (event) => {
    // eslint-disable-next-line no-console
    console.error('Unhandled promise rejection:', event.reason)

    // 如果是 API 错误，使用统一错误处理
    if (event.reason && typeof event.reason === 'object') {
      handleApiError(event.reason)
    } else {
      toast.error('发生未知错误', {
        description: '请刷新页面重试'
      })
    }

    // 阻止默认的错误处理
    event.preventDefault()
  })

  // 处理 JavaScript 运行时错误
  window.addEventListener('error', (event) => {
    // eslint-disable-next-line no-console
    console.error('Global error:', event.error)

    toast.error('页面运行错误', {
      description: '请刷新页面重试',
      action: {
        label: '刷新',
        onClick: () => window.location.reload()
      }
    })
  })
}

// 导出错误类型判断函数
export const isNetworkError = (error: any): error is NetworkError => {
  return error && error.code && ['NETWORK_ERROR', 'TIMEOUT', 'CANCELLED'].includes(error.code)
}

export const isHttpError = (error: any): error is HttpError => {
  return error && typeof error.status === 'number'
}

export const isBusinessError = (error: any): error is BusinessError => {
  return error && error.code && typeof error.code === 'string' && !isNetworkError(error)
}
