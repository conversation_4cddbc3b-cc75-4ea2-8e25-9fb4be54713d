/**
 * API 统一入口
 * 导出所有 API 相关功能
 */

// 核心客户端
export { api, ApiClient, apiClient, axios } from './client'

// 类型定义
export type * from './types'

// 配置
export * from './config'

// 工具函数
export * from './utils'

// 错误处理
export * from './error-handler'

// 拦截器
export * from './interceptors'

// React Query Hooks
export * from './hooks'

// API 端点
export * from './endpoints'

// 便捷导出
export {
  // 客户端方法
  get,
  post,
  put,
  patch,
  del as delete,
  upload,
  download,
  batch,
  concurrent,

  // 缓存管理
  clearCache,
  clearDedupe,
  getRequestStats,
  resetRequestStats,
} from './client'

// 全局初始化函数
export async function initializeAPI() {
  // 设置全局错误处理
  const { setupGlobalErrorHandling } = await import('./error-handler')
  setupGlobalErrorHandling()

  // 初始化统计
  const { StatsManager, CacheManager } = await import('./utils')
  StatsManager.load()

  // 定期清理缓存
  setInterval(() => {
    CacheManager.cleanup()
  }, 5 * 60 * 1000) // 5分钟清理一次
}

// 默认导出 API 客户端
// export default api
