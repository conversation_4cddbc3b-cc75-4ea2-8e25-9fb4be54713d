/**
 * Axios 客户端配置
 * 创建和配置 axios 实例
 */

import axios, { type AxiosInstance, type AxiosRequestConfig } from 'axios'
import type { ApiResponse, ApiRequestConfig, UploadConfig, DownloadConfig } from './types'
import { apiConfig } from './config'
import {
  createRequestInterceptor,
  createResponseInterceptor,
  createRetryInterceptor,
  createDedupeInterceptor
} from './interceptors'
import { DedupeManager, CacheManager, StatsManager } from './utils'

// 创建 axios 实例
export const apiClient: AxiosInstance = axios.create({
  baseURL: apiConfig.client.baseURL,
  timeout: apiConfig.client.timeout,
  headers: apiConfig.client.headers,
  withCredentials: apiConfig.client.withCredentials,
})

// 添加请求拦截器
const requestInterceptor = createRequestInterceptor()
apiClient.interceptors.request.use(
  requestInterceptor.onFulfilled,
  requestInterceptor.onRejected
)

// 添加去重拦截器
const dedupeInterceptor = createDedupeInterceptor()
apiClient.interceptors.request.use(
  dedupeInterceptor.onFulfilled,
  dedupeInterceptor.onRejected
)

// 添加响应拦截器
const responseInterceptor = createResponseInterceptor()
apiClient.interceptors.response.use(
  responseInterceptor.onFulfilled,
  responseInterceptor.onRejected
)

// 添加重试拦截器
const retryInterceptor = createRetryInterceptor(
  apiConfig.client.defaultRetryCount,
  apiConfig.client.retryDelay
)
apiClient.interceptors.response.use(undefined, retryInterceptor.onRejected)

// API 客户端类
export class ApiClient {
  private instance: AxiosInstance

  constructor(instance: AxiosInstance = apiClient) {
    this.instance = instance
  }

  // 通用请求方法
  async request<T = any>(config: AxiosRequestConfig & ApiRequestConfig): Promise<ApiResponse<T>> {
    // 检查缓存
    if (config.method?.toUpperCase() === 'GET' && config.cacheTime) {
      const cacheKey = `${config.url}_${JSON.stringify(config.params || {})}`
      const cached = CacheManager.get<ApiResponse<T>>(cacheKey)
      if (cached) {
        return cached
      }
    }

    // 检查请求去重
    if (config.dedupe !== false) {
      const dedupeKey = DedupeManager.generateKey(
        config.method || 'GET',
        config.url || '',
        config.data
      )

      if (DedupeManager.has(dedupeKey)) {
        return DedupeManager.get(dedupeKey)!
      }

      const requestPromise = this.instance.request<ApiResponse<T>>(config)
      DedupeManager.add(dedupeKey, requestPromise)

      const response = await requestPromise

      // 缓存 GET 请求结果
      if (config.method?.toUpperCase() === 'GET' && config.cacheTime) {
        const cacheKey = `${config.url}_${JSON.stringify(config.params || {})}`
        CacheManager.set(cacheKey, response.data, config.cacheTime)
      }

      return response.data
    }

    const response = await this.instance.request<ApiResponse<T>>(config)
    return response.data
  }

  // GET 请求
  async get<T = any>(
    url: string,
    params?: Record<string, any>,
    config?: ApiRequestConfig
  ): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'GET',
      url,
      params,
      ...config,
    })
  }

  // POST 请求
  async post<T = any>(
    url: string,
    data?: any,
    config?: ApiRequestConfig
  ): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'POST',
      url,
      data,
      ...config,
    })
  }

  // PUT 请求
  async put<T = any>(
    url: string,
    data?: any,
    config?: ApiRequestConfig
  ): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'PUT',
      url,
      data,
      ...config,
    })
  }

  // PATCH 请求
  async patch<T = any>(
    url: string,
    data?: any,
    config?: ApiRequestConfig
  ): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'PATCH',
      url,
      data,
      ...config,
    })
  }

  // DELETE 请求
  async delete<T = any>(
    url: string,
    config?: ApiRequestConfig
  ): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'DELETE',
      url,
      ...config,
    })
  }

  // 文件上传
  async upload<T = any>(
    url: string,
    file: File | FileList | FormData,
    config?: UploadConfig
  ): Promise<ApiResponse<T>> {
    let formData: FormData

    if (file instanceof FormData) {
      formData = file
    } else if (file instanceof FileList) {
      formData = new FormData()
      Array.from(file).forEach((f, index) => {
        formData.append(config?.multiple ? `files[${index}]` : 'file', f)
      })
    } else {
      formData = new FormData()
      formData.append('file', file)
    }

    // 文件类型验证
    if (config?.accept && file instanceof File) {
      const isValidType = config.accept.some(type => {
        if (type.includes('*')) {
          return file.type.startsWith(type.split('/')[0])
        }
        return file.type === type
      })

      if (!isValidType) {
        throw new Error(`File type ${file.type} is not allowed`)
      }
    }

    // 文件大小验证
    if (config?.maxSize && file instanceof File && file.size > config.maxSize) {
      throw new Error(`File size ${file.size} exceeds maximum ${config.maxSize}`)
    }

    return this.request<T>({
      method: 'POST',
      url,
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (config?.onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          config.onProgress(progress)
        }
      },
      ...config,
    })
  }

  // 文件下载
  async download(
    url: string,
    config?: DownloadConfig
  ): Promise<Blob> {
    const response = await this.instance.request({
      method: 'GET',
      url,
      responseType: 'blob',
      onDownloadProgress: (progressEvent) => {
        if (config?.onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          config.onProgress(progress)
        }
      },
      ...config,
    })

    // 自动下载文件
    if (config?.filename) {
      const blob = response.data
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = config.filename

      if (config.openInNewTab) {
        link.target = '_blank'
      }

      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(downloadUrl)
    }

    return response.data
  }

  // 批量请求
  async batch<T = any>(
    requests: Array<AxiosRequestConfig & ApiRequestConfig>
  ): Promise<Array<ApiResponse<T>>> {
    const promises = requests.map(config => this.request<T>(config))
    return Promise.all(promises)
  }

  // 并发请求（带限制）
  async concurrent<T = any>(
    requests: Array<AxiosRequestConfig & ApiRequestConfig>,
    limit: number = 5
  ): Promise<Array<ApiResponse<T>>> {
    const results: Array<ApiResponse<T>> = []

    for (let i = 0; i < requests.length; i += limit) {
      const batch = requests.slice(i, i + limit)
      const batchResults = await this.batch<T>(batch)
      results.push(...batchResults)
    }

    return results
  }

  // 取消请求
  createCancelToken() {
    return axios.CancelToken.source()
  }

  // 检查请求是否被取消
  isCancel(error: any): boolean {
    return axios.isCancel(error)
  }
}

// 创建默认客户端实例
export const api = new ApiClient(apiClient)

// 导出便捷方法
export const { get, post, put, patch, delete: del, upload, download, batch, concurrent } = api

// 导出原始 axios 实例（用于特殊需求）
export { apiClient as axios }

// 清理缓存的方法
export const clearCache = () => {
  CacheManager.clear()
}

// 清理去重缓存的方法
export const clearDedupe = () => {
  DedupeManager.clear()
}

// 获取请求统计
export const getRequestStats = () => {
  return StatsManager.getStats()
}

// 重置请求统计
export const resetRequestStats = () => {
  StatsManager.reset()
}
