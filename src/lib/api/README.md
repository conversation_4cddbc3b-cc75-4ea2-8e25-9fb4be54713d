# 企业级 API 请求解决方案

基于 React 19 + TypeScript + TanStack Router + Shadcn UI 的统一 API 请求解决方案，使用 axios 和 @tanstack/react-query 封装。

## 🚀 特性

- ✅ **统一错误处理** - HTTP 状态码、业务逻辑错误、网络错误的统一处理
- ✅ **自动认证管理** - JWT token 自动添加、刷新和过期处理
- ✅ **请求/响应拦截** - 统一的 loading、错误提示、数据标准化
- ✅ **TypeScript 类型安全** - 完整的类型定义和自动推导
- ✅ **性能优化** - 请求去重、缓存、重试、取消等机制
- ✅ **安全防护** - XSS 防护、CSRF 保护、数据加密
- ✅ **文件上传/下载** - 支持进度显示、分片上传、断点续传
- ✅ **国际化支持** - 错误消息本地化
- ✅ **开发体验** - 详细日志、错误监控、调试工具

## 📁 目录结构

```
src/lib/api/
├── client.ts          # axios 实例配置
├── types.ts           # API 类型定义
├── config.ts          # 环境配置和常量
├── utils.ts           # 工具函数
├── interceptors.ts    # 请求/响应拦截器
├── error-handler.ts   # 统一错误处理
├── hooks/             # React Query hooks
│   ├── index.ts
│   ├── use-api-query.ts
│   ├── use-api-mutation.ts
│   ├── use-api-infinite-query.ts
│   ├── use-upload.ts
│   └── use-download.ts
├── endpoints/         # API 端点定义
│   ├── index.ts
│   ├── auth.ts
│   ├── users.ts
│   ├── files.ts
│   └── system.ts
└── index.ts           # 主入口文件
```

## 🔧 安装和配置

### 1. 环境变量配置

在 `.env` 文件中添加：

```env
# API 配置
VITE_API_BASE_URL=http://localhost:3001/api
VITE_APP_VERSION=1.0.0

# 安全配置
VITE_ENCRYPTION_KEY=your-encryption-key
VITE_ENCRYPTION_IV=your-encryption-iv

# 功能开关
VITE_ENABLE_MOCK=false
```

### 2. 初始化 API

在应用入口文件中初始化：

```tsx
// src/main.tsx
import { initializeAPI } from '@/lib/api'

// 初始化 API
initializeAPI()

// 其他应用初始化代码...
```

### 3. 配置 React Query

```tsx
// src/App.tsx
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5分钟
      cacheTime: 10 * 60 * 1000, // 10分钟
      retry: 3,
      refetchOnWindowFocus: false,
    },
    mutations: {
      retry: 1,
    },
  },
})

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      {/* 你的应用组件 */}
    </QueryClientProvider>
  )
}
```

## 📖 使用指南

### 基础 API 调用

```tsx
import { api } from '@/lib/api'

// GET 请求
const users = await api.get('/users', { page: 1, pageSize: 10 })

// POST 请求
const newUser = await api.post('/users', {
  name: 'John Doe',
  email: '<EMAIL>'
})

// PUT 请求
const updatedUser = await api.put('/users/1', { name: 'Jane Doe' })

// DELETE 请求
await api.delete('/users/1')
```

### 使用 React Query Hooks

```tsx
import { useApiQuery, useApiMutation } from '@/lib/api'

function UserList() {
  // 查询用户列表
  const { data: users, isLoading, error } = useApiQuery({
    url: '/users',
    params: { page: 1, pageSize: 10 },
    queryKey: ['users'],
  })

  // 创建用户
  const createUserMutation = useApiMutation(
    (userData) => api.post('/users', userData),
    {
      onSuccess: () => {
        // 成功后刷新用户列表
        queryClient.invalidateQueries(['users'])
      },
    }
  )

  const handleCreateUser = () => {
    createUserMutation.mutate({
      name: 'New User',
      email: '<EMAIL>'
    })
  }

  if (isLoading) return <div>Loading...</div>
  if (error) return <div>Error: {error.message}</div>

  return (
    <div>
      <button onClick={handleCreateUser}>Create User</button>
      {users?.data.map(user => (
        <div key={user.id}>{user.name}</div>
      ))}
    </div>
  )
}
```

### 便捷的业务 Hooks

```tsx
import { useUsers, useCreateUser, useUpdateUser } from '@/lib/api'

function UserManagement() {
  // 获取用户列表
  const { data: users, isLoading } = useUsers({ page: 1, pageSize: 10 })
  
  // 创建用户
  const createUser = useCreateUser()
  
  // 更新用户
  const updateUser = useUpdateUser()

  const handleCreate = () => {
    createUser.mutate({
      name: 'John Doe',
      email: '<EMAIL>'
    })
  }

  const handleUpdate = (id: string) => {
    updateUser.mutate({
      id,
      name: 'Updated Name'
    })
  }

  return (
    <div>
      <button onClick={handleCreate}>Create User</button>
      {users?.data.items.map(user => (
        <div key={user.id}>
          {user.name}
          <button onClick={() => handleUpdate(user.id)}>Update</button>
        </div>
      ))}
    </div>
  )
}
```

### 文件上传

```tsx
import { useUpload } from '@/lib/api'

function FileUpload() {
  const {
    uploadFile,
    isUploading,
    progress,
    uploadedFiles,
    error
  } = useUpload({
    endpoint: '/files/upload',
    accept: ['image/*'],
    maxSize: 5 * 1024 * 1024, // 5MB
    onUploadSuccess: (file) => {
      console.log('Upload success:', file)
    }
  })

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      uploadFile(file)
    }
  }

  return (
    <div>
      <input type="file" onChange={handleFileSelect} />
      {isUploading && (
        <div>
          <div>Uploading... {progress}%</div>
          <progress value={progress} max={100} />
        </div>
      )}
      {error && <div>Error: {error}</div>}
      {uploadedFiles.map(file => (
        <div key={file.id}>
          <img src={file.url} alt={file.name} />
        </div>
      ))}
    </div>
  )
}
```

### 拖拽上传

```tsx
import { useDragUpload } from '@/lib/api'

function DragUpload() {
  const {
    uploadFiles,
    isDragOver,
    dragProps,
    isUploading,
    progress
  } = useDragUpload({
    multiple: true,
    accept: ['image/*']
  })

  return (
    <div
      {...dragProps}
      className={`border-2 border-dashed p-8 ${
        isDragOver ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
      }`}
    >
      {isUploading ? (
        <div>Uploading... {progress}%</div>
      ) : (
        <div>Drag and drop files here</div>
      )}
    </div>
  )
}
```

### 文件下载

```tsx
import { useDownload } from '@/lib/api'

function FileDownload() {
  const {
    downloadFile,
    isDownloading,
    progress,
    error
  } = useDownload({
    onDownloadSuccess: (blob, filename) => {
      console.log('Download complete:', filename)
    }
  })

  const handleDownload = () => {
    downloadFile('/files/123/download', 'document.pdf')
  }

  return (
    <div>
      <button onClick={handleDownload} disabled={isDownloading}>
        {isDownloading ? `Downloading... ${progress}%` : 'Download File'}
      </button>
      {error && <div>Error: {error}</div>}
    </div>
  )
}
```

### 无限滚动

```tsx
import { useApiInfiniteQuery, getAllPagesData } from '@/lib/api'

function InfiniteUserList() {
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading
  } = useApiInfiniteQuery({
    url: '/users',
    pageSize: 20,
    queryKey: ['users', 'infinite']
  })

  const users = getAllPagesData(data)

  return (
    <div>
      {users.map(user => (
        <div key={user.id}>{user.name}</div>
      ))}
      
      {hasNextPage && (
        <button
          onClick={() => fetchNextPage()}
          disabled={isFetchingNextPage}
        >
          {isFetchingNextPage ? 'Loading...' : 'Load More'}
        </button>
      )}
    </div>
  )
}
```

### 错误处理

```tsx
import { handleApiError, isNetworkError, isHttpError } from '@/lib/api'

function CustomErrorHandling() {
  const handleRequest = async () => {
    try {
      const result = await api.get('/users')
      console.log(result)
    } catch (error) {
      // 使用统一错误处理
      const processedError = handleApiError(error, {
        showError: false, // 不显示默认错误提示
        customErrorHandler: (err) => {
          if (isNetworkError(err)) {
            // 处理网络错误
            console.log('Network error:', err.message)
          } else if (isHttpError(err) && err.status === 404) {
            // 处理 404 错误
            console.log('Resource not found')
          } else {
            // 处理其他错误
            console.log('Other error:', err.message)
          }
        }
      })
    }
  }

  return <button onClick={handleRequest}>Make Request</button>
}
```

## 🔒 安全特性

### 自动认证

API 客户端会自动：
- 添加 JWT token 到请求头
- 在 token 过期时自动刷新
- 在刷新失败时跳转到登录页面

### XSS 防护

所有请求数据会自动进行 XSS 清理：

```tsx
// 输入数据会被自动清理
api.post('/users', {
  name: '<script>alert("xss")</script>John', // 会被清理为 'John'
  bio: 'javascript:alert("xss")' // 会被清理
})
```

### CSRF 保护

自动添加 CSRF token 到请求头。

### 数据加密

可以启用请求数据加密：

```tsx
api.post('/sensitive-data', data, {
  encrypt: true // 启用数据加密
})
```

## 📊 监控和调试

### 请求统计

```tsx
import { getRequestStats } from '@/lib/api'

const stats = getRequestStats()
console.log('API Statistics:', stats)
// {
//   total: 150,
//   success: 140,
//   error: 10,
//   pending: 2,
//   averageTime: 250
// }
```

### 开发环境日志

在开发环境下，所有 API 请求和响应都会在控制台输出详细日志。

## 🎯 最佳实践

1. **使用 TypeScript** - 充分利用类型安全
2. **合理使用缓存** - 设置适当的 staleTime 和 cacheTime
3. **错误边界** - 在组件层面处理错误
4. **加载状态** - 提供良好的用户反馈
5. **乐观更新** - 提升用户体验
6. **请求去重** - 避免重复请求
7. **适当重试** - 处理临时网络问题

## 🔧 高级配置

### 自定义拦截器

```tsx
import { apiClient } from '@/lib/api'

// 添加自定义请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    // 自定义请求处理
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 添加自定义响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    // 自定义响应处理
    return response
  },
  (error) => {
    return Promise.reject(error)
  }
)
```

### Mock 数据

在开发环境中启用 Mock：

```env
VITE_ENABLE_MOCK=true
```

```tsx
// 在 API 端点中定义 Mock 数据
export const mockUsers = [
  { id: '1', name: 'John Doe', email: '<EMAIL>' },
  { id: '2', name: 'Jane Smith', email: '<EMAIL>' }
]
```

这套 API 解决方案提供了企业级应用所需的所有功能，包括安全性、性能优化、错误处理和开发体验。通过统一的接口和 TypeScript 类型安全，可以大大提升开发效率和代码质量。
