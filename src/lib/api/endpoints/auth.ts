/**
 * 认证相关 API 端点
 */

import { api } from '../client'
import type { ApiResponse } from '../types'

// 认证相关类型定义
export interface LoginRequest {
  email: string
  password: string
  rememberMe?: boolean
}

export interface LoginResponse {
  user: {
    id: string
    email: string
    name: string
    avatar?: string
    role: string
  }
  accessToken: string
  refreshToken: string
  expiresIn: number
}

export interface RegisterRequest {
  name: string
  email: string
  password: string
  confirmPassword: string
}

export interface ForgotPasswordRequest {
  email: string
}

export interface ResetPasswordRequest {
  token: string
  password: string
  confirmPassword: string
}

export interface ChangePasswordRequest {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

export interface RefreshTokenRequest {
  refreshToken: string
}

// 认证 API 类
export class AuthAPI {
  // 登录
  static async login(data: LoginRequest): Promise<ApiResponse<LoginResponse>> {
    return api.post('/auth/login', data, {
      requireAuth: false,
      showLoading: true,
      showSuccess: true,
      successMessage: '登录成功',
    })
  }

  // 注册
  static async register(data: RegisterRequest): Promise<ApiResponse<{ message: string }>> {
    return api.post('/auth/register', data, {
      requireAuth: false,
      showLoading: true,
      showSuccess: true,
      successMessage: '注册成功',
    })
  }

  // 登出
  static async logout(): Promise<ApiResponse<{ message: string }>> {
    return api.post('/auth/logout', {}, {
      showLoading: true,
      showSuccess: true,
      successMessage: '退出成功',
    })
  }

  // 刷新令牌
  static async refreshToken(data: RefreshTokenRequest): Promise<ApiResponse<LoginResponse>> {
    return api.post('/auth/refresh', data, {
      requireAuth: false,
      showLoading: false,
      showError: false,
    })
  }

  // 获取用户信息
  static async getProfile(): Promise<ApiResponse<LoginResponse['user']>> {
    return api.get('/auth/profile', {}, {
      showLoading: false,
    })
  }

  // 更新用户信息
  static async updateProfile(data: Partial<LoginResponse['user']>): Promise<ApiResponse<LoginResponse['user']>> {
    return api.put('/auth/profile', data, {
      showLoading: true,
      showSuccess: true,
      successMessage: '个人信息更新成功',
    })
  }

  // 忘记密码
  static async forgotPassword(data: ForgotPasswordRequest): Promise<ApiResponse<{ message: string }>> {
    return api.post('/auth/forgot-password', data, {
      requireAuth: false,
      showLoading: true,
      showSuccess: true,
      successMessage: '重置密码邮件已发送',
    })
  }

  // 重置密码
  static async resetPassword(data: ResetPasswordRequest): Promise<ApiResponse<{ message: string }>> {
    return api.post('/auth/reset-password', data, {
      requireAuth: false,
      showLoading: true,
      showSuccess: true,
      successMessage: '密码重置成功',
    })
  }

  // 修改密码
  static async changePassword(data: ChangePasswordRequest): Promise<ApiResponse<{ message: string }>> {
    return api.post('/auth/change-password', data, {
      showLoading: true,
      showSuccess: true,
      successMessage: '密码修改成功',
    })
  }

  // 验证邮箱
  static async verifyEmail(token: string): Promise<ApiResponse<{ message: string }>> {
    return api.post('/auth/verify-email', { token }, {
      requireAuth: false,
      showLoading: true,
      showSuccess: true,
      successMessage: '邮箱验证成功',
    })
  }

  // 重新发送验证邮件
  static async resendVerificationEmail(): Promise<ApiResponse<{ message: string }>> {
    return api.post('/auth/resend-verification', {}, {
      showLoading: true,
      showSuccess: true,
      successMessage: '验证邮件已重新发送',
    })
  }

  // 检查邮箱是否可用
  static async checkEmailAvailability(email: string): Promise<ApiResponse<{ available: boolean }>> {
    return api.get('/auth/check-email', { email }, {
      showLoading: false,
      showError: false,
    })
  }

  // 获取登录历史
  static async getLoginHistory(params?: {
    page?: number
    pageSize?: number
  }): Promise<ApiResponse<{
    items: Array<{
      id: string
      ip: string
      userAgent: string
      location?: string
      loginAt: string
      success: boolean
    }>
    total: number
    page: number
    pageSize: number
  }>> {
    return api.get('/auth/login-history', params, {
      showLoading: false,
    })
  }

  // 获取活跃会话
  static async getActiveSessions(): Promise<ApiResponse<Array<{
    id: string
    ip: string
    userAgent: string
    location?: string
    lastActivity: string
    current: boolean
  }>>> {
    return api.get('/auth/sessions', {}, {
      showLoading: false,
    })
  }

  // 终止会话
  static async terminateSession(sessionId: string): Promise<ApiResponse<{ message: string }>> {
    return api.delete(`/auth/sessions/${sessionId}`, {
      showLoading: true,
      showSuccess: true,
      successMessage: '会话已终止',
    })
  }

  // 终止所有其他会话
  static async terminateAllOtherSessions(): Promise<ApiResponse<{ message: string }>> {
    return api.post('/auth/terminate-all-sessions', {}, {
      showLoading: true,
      showSuccess: true,
      successMessage: '所有其他会话已终止',
    })
  }
}

// 导出便捷方法
export const {
  login,
  register,
  logout,
  refreshToken,
  getProfile,
  updateProfile,
  forgotPassword,
  resetPassword,
  changePassword,
  verifyEmail,
  resendVerificationEmail,
  checkEmailAvailability,
  getLoginHistory,
  getActiveSessions,
  terminateSession,
  terminateAllOtherSessions,
} = AuthAPI
