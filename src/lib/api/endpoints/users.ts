/**
 * 用户相关 API 端点
 */

import { api } from '../client'
import type { ApiResponse, PaginatedResponse } from '../types'

// 用户相关类型定义
export interface User {
  id: string
  name: string
  email: string
  avatar?: string
  role: string
  status: 'active' | 'inactive' | 'suspended'
  createdAt: string
  updatedAt: string
  lastLoginAt?: string
  profile?: {
    bio?: string
    phone?: string
    address?: string
    website?: string
    socialLinks?: {
      twitter?: string
      linkedin?: string
      github?: string
    }
  }
}

export interface CreateUserRequest {
  name: string
  email: string
  password: string
  role?: string
  profile?: User['profile']
}

export interface UpdateUserRequest {
  name?: string
  email?: string
  role?: string
  status?: User['status']
  profile?: User['profile']
}

export interface UserListParams {
  page?: number
  pageSize?: number
  search?: string
  role?: string
  status?: string
  sortBy?: 'name' | 'email' | 'createdAt' | 'lastLoginAt'
  sortOrder?: 'asc' | 'desc'
}

// 用户 API 类
export class UsersAPI {
  // 获取用户列表
  static async getUsers(params?: UserListParams): Promise<PaginatedResponse<User>> {
    return api.get('/users', params, {
      showLoading: false,
    })
  }

  // 获取用户详情
  static async getUser(id: string): Promise<ApiResponse<User>> {
    return api.get(`/users/${id}`, {}, {
      showLoading: false,
    })
  }

  // 创建用户
  static async createUser(data: CreateUserRequest): Promise<ApiResponse<User>> {
    return api.post('/users', data, {
      showLoading: true,
      showSuccess: true,
      successMessage: '用户创建成功',
    })
  }

  // 更新用户
  static async updateUser(id: string, data: UpdateUserRequest): Promise<ApiResponse<User>> {
    return api.put(`/users/${id}`, data, {
      showLoading: true,
      showSuccess: true,
      successMessage: '用户更新成功',
    })
  }

  // 删除用户
  static async deleteUser(id: string): Promise<ApiResponse<{ message: string }>> {
    return api.delete(`/users/${id}`, {
      showLoading: true,
      showSuccess: true,
      successMessage: '用户删除成功',
    })
  }

  // 批量删除用户
  static async deleteUsers(ids: string[]): Promise<ApiResponse<{ message: string; deletedCount: number }>> {
    return api.post('/users/batch-delete', { ids }, {
      showLoading: true,
      showSuccess: true,
      successMessage: '用户批量删除成功',
    })
  }

  // 启用/禁用用户
  static async toggleUserStatus(id: string, status: User['status']): Promise<ApiResponse<User>> {
    return api.patch(`/users/${id}/status`, { status }, {
      showLoading: true,
      showSuccess: true,
      successMessage: `用户已${status === 'active' ? '启用' : '禁用'}`,
    })
  }

  // 重置用户密码
  static async resetUserPassword(id: string): Promise<ApiResponse<{ temporaryPassword: string }>> {
    return api.post(`/users/${id}/reset-password`, {}, {
      showLoading: true,
      showSuccess: true,
      successMessage: '密码重置成功',
    })
  }

  // 上传用户头像
  static async uploadAvatar(id: string, file: File): Promise<ApiResponse<{ avatarUrl: string }>> {
    return api.upload(`/users/${id}/avatar`, file, {
      showLoading: true,
      showSuccess: true,
      successMessage: '头像上传成功',
      accept: ['image/*'],
      maxSize: 5 * 1024 * 1024, // 5MB
    })
  }

  // 删除用户头像
  static async deleteAvatar(id: string): Promise<ApiResponse<{ message: string }>> {
    return api.delete(`/users/${id}/avatar`, {
      showLoading: true,
      showSuccess: true,
      successMessage: '头像删除成功',
    })
  }

  // 获取用户权限
  static async getUserPermissions(id: string): Promise<ApiResponse<{
    permissions: string[]
    roles: Array<{
      id: string
      name: string
      permissions: string[]
    }>
  }>> {
    return api.get(`/users/${id}/permissions`, {}, {
      showLoading: false,
    })
  }

  // 更新用户权限
  static async updateUserPermissions(
    id: string, 
    data: { permissions: string[]; roles: string[] }
  ): Promise<ApiResponse<{ message: string }>> {
    return api.put(`/users/${id}/permissions`, data, {
      showLoading: true,
      showSuccess: true,
      successMessage: '权限更新成功',
    })
  }

  // 获取用户活动日志
  static async getUserActivityLog(
    id: string,
    params?: {
      page?: number
      pageSize?: number
      startDate?: string
      endDate?: string
      action?: string
    }
  ): Promise<PaginatedResponse<{
    id: string
    action: string
    resource: string
    details: Record<string, any>
    ip: string
    userAgent: string
    createdAt: string
  }>> {
    return api.get(`/users/${id}/activity-log`, params, {
      showLoading: false,
    })
  }

  // 导出用户数据
  static async exportUsers(params?: {
    format?: 'csv' | 'xlsx' | 'json'
    filters?: UserListParams
  }): Promise<Blob> {
    return api.download('/users/export', {
      ...params,
      filename: `users_export_${new Date().toISOString().split('T')[0]}.${params?.format || 'csv'}`,
      showLoading: true,
      showSuccess: true,
      successMessage: '用户数据导出成功',
    })
  }

  // 导入用户数据
  static async importUsers(file: File): Promise<ApiResponse<{
    imported: number
    failed: number
    errors: Array<{ row: number; error: string }>
  }>> {
    return api.upload('/users/import', file, {
      showLoading: true,
      showSuccess: true,
      successMessage: '用户数据导入成功',
      accept: ['.csv', '.xlsx'],
      maxSize: 10 * 1024 * 1024, // 10MB
    })
  }

  // 搜索用户
  static async searchUsers(query: string, limit: number = 10): Promise<ApiResponse<User[]>> {
    return api.get('/users/search', { q: query, limit }, {
      showLoading: false,
      showError: false,
    })
  }

  // 获取用户统计
  static async getUserStats(): Promise<ApiResponse<{
    total: number
    active: number
    inactive: number
    suspended: number
    newThisMonth: number
    growthRate: number
    roleDistribution: Record<string, number>
  }>> {
    return api.get('/users/stats', {}, {
      showLoading: false,
    })
  }

  // 发送用户通知
  static async sendNotification(
    id: string,
    data: {
      title: string
      message: string
      type?: 'info' | 'success' | 'warning' | 'error'
      channels?: ('email' | 'sms' | 'push')[]
    }
  ): Promise<ApiResponse<{ message: string }>> {
    return api.post(`/users/${id}/notifications`, data, {
      showLoading: true,
      showSuccess: true,
      successMessage: '通知发送成功',
    })
  }

  // 批量发送通知
  static async sendBulkNotification(
    data: {
      userIds: string[]
      title: string
      message: string
      type?: 'info' | 'success' | 'warning' | 'error'
      channels?: ('email' | 'sms' | 'push')[]
    }
  ): Promise<ApiResponse<{ message: string; sentCount: number }>> {
    return api.post('/users/bulk-notifications', data, {
      showLoading: true,
      showSuccess: true,
      successMessage: '批量通知发送成功',
    })
  }
}

// 导出便捷方法
export const {
  getUsers,
  getUser,
  createUser,
  updateUser,
  deleteUser,
  deleteUsers,
  toggleUserStatus,
  resetUserPassword,
  uploadAvatar,
  deleteAvatar,
  getUserPermissions,
  updateUserPermissions,
  getUserActivityLog,
  exportUsers,
  importUsers,
  searchUsers,
  getUserStats,
  sendNotification,
  sendBulkNotification,
} = UsersAPI
