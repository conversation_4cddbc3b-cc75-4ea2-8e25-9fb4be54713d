/**
 * 系统相关 API 端点
 */

import { api } from '../client'
import type { ApiResponse } from '../types'

// 系统相关类型定义
export interface SystemHealth {
  status: 'healthy' | 'degraded' | 'unhealthy'
  timestamp: string
  services: {
    database: {
      status: 'up' | 'down'
      responseTime: number
    }
    redis: {
      status: 'up' | 'down'
      responseTime: number
    }
    storage: {
      status: 'up' | 'down'
      responseTime: number
    }
    email: {
      status: 'up' | 'down'
      responseTime: number
    }
  }
  metrics: {
    uptime: number
    memory: {
      used: number
      total: number
      percentage: number
    }
    cpu: {
      usage: number
    }
    disk: {
      used: number
      total: number
      percentage: number
    }
  }
}

export interface SystemConfig {
  app: {
    name: string
    version: string
    environment: string
    debug: boolean
  }
  features: {
    registration: boolean
    emailVerification: boolean
    socialLogin: boolean
    fileUpload: boolean
    notifications: boolean
  }
  limits: {
    maxFileSize: number
    maxFilesPerUpload: number
    rateLimit: {
      requests: number
      window: number
    }
  }
  integrations: {
    email: {
      provider: string
      enabled: boolean
    }
    storage: {
      provider: string
      enabled: boolean
    }
    analytics: {
      provider: string
      enabled: boolean
    }
  }
}

export interface SystemStats {
  users: {
    total: number
    active: number
    newToday: number
    newThisWeek: number
    newThisMonth: number
  }
  files: {
    total: number
    totalSize: number
    uploadedToday: number
    uploadedThisWeek: number
    uploadedThisMonth: number
  }
  api: {
    totalRequests: number
    requestsToday: number
    averageResponseTime: number
    errorRate: number
  }
  system: {
    uptime: number
    version: string
    lastDeployment: string
  }
}

// 系统 API 类
export class SystemAPI {
  // 健康检查
  static async getHealth(): Promise<ApiResponse<SystemHealth>> {
    return api.get('/system/health', {}, {
      requireAuth: false,
      showLoading: false,
      showError: false,
    })
  }

  // 获取系统配置
  static async getConfig(): Promise<ApiResponse<SystemConfig>> {
    return api.get('/system/config', {}, {
      requireAuth: false,
      showLoading: false,
    })
  }

  // 更新系统配置
  static async updateConfig(data: Partial<SystemConfig>): Promise<ApiResponse<SystemConfig>> {
    return api.put('/system/config', data, {
      showLoading: true,
      showSuccess: true,
      successMessage: '系统配置更新成功',
    })
  }

  // 获取系统统计
  static async getStats(): Promise<ApiResponse<SystemStats>> {
    return api.get('/system/stats', {}, {
      showLoading: false,
    })
  }

  // 获取系统版本信息
  static async getVersion(): Promise<ApiResponse<{
    version: string
    buildTime: string
    gitCommit: string
    environment: string
  }>> {
    return api.get('/system/version', {}, {
      requireAuth: false,
      showLoading: false,
    })
  }

  // 获取系统日志
  static async getLogs(params?: {
    level?: 'error' | 'warn' | 'info' | 'debug'
    startDate?: string
    endDate?: string
    page?: number
    pageSize?: number
  }): Promise<ApiResponse<{
    logs: Array<{
      id: string
      level: string
      message: string
      timestamp: string
      metadata?: Record<string, any>
    }>
    total: number
    page: number
    pageSize: number
  }>> {
    return api.get('/system/logs', params, {
      showLoading: false,
    })
  }

  // 清理系统缓存
  static async clearCache(cacheType?: 'all' | 'api' | 'files' | 'sessions'): Promise<ApiResponse<{ message: string }>> {
    return api.post('/system/clear-cache', { type: cacheType || 'all' }, {
      showLoading: true,
      showSuccess: true,
      successMessage: '缓存清理成功',
    })
  }

  // 系统备份
  static async createBackup(): Promise<ApiResponse<{
    backupId: string
    filename: string
    size: number
    createdAt: string
  }>> {
    return api.post('/system/backup', {}, {
      showLoading: true,
      showSuccess: true,
      successMessage: '系统备份创建成功',
    })
  }

  // 获取备份列表
  static async getBackups(): Promise<ApiResponse<Array<{
    id: string
    filename: string
    size: number
    createdAt: string
    status: 'completed' | 'failed' | 'in_progress'
  }>>> {
    return api.get('/system/backups', {}, {
      showLoading: false,
    })
  }

  // 下载备份
  static async downloadBackup(backupId: string): Promise<Blob> {
    return api.download(`/system/backups/${backupId}/download`, {
      showLoading: true,
      showSuccess: true,
      successMessage: '备份下载完成',
    })
  }

  // 删除备份
  static async deleteBackup(backupId: string): Promise<ApiResponse<{ message: string }>> {
    return api.delete(`/system/backups/${backupId}`, {
      showLoading: true,
      showSuccess: true,
      successMessage: '备份删除成功',
    })
  }

  // 系统维护模式
  static async enableMaintenanceMode(message?: string): Promise<ApiResponse<{ message: string }>> {
    return api.post('/system/maintenance', { enabled: true, message }, {
      showLoading: true,
      showSuccess: true,
      successMessage: '维护模式已启用',
    })
  }

  static async disableMaintenanceMode(): Promise<ApiResponse<{ message: string }>> {
    return api.post('/system/maintenance', { enabled: false }, {
      showLoading: true,
      showSuccess: true,
      successMessage: '维护模式已关闭',
    })
  }

  // 获取维护模式状态
  static async getMaintenanceStatus(): Promise<ApiResponse<{
    enabled: boolean
    message?: string
    enabledAt?: string
  }>> {
    return api.get('/system/maintenance', {}, {
      requireAuth: false,
      showLoading: false,
    })
  }

  // 发送测试邮件
  static async sendTestEmail(email: string): Promise<ApiResponse<{ message: string }>> {
    return api.post('/system/test-email', { email }, {
      showLoading: true,
      showSuccess: true,
      successMessage: '测试邮件发送成功',
    })
  }

  // 测试存储连接
  static async testStorage(): Promise<ApiResponse<{
    status: 'success' | 'failed'
    message: string
    responseTime: number
  }>> {
    return api.post('/system/test-storage', {}, {
      showLoading: true,
    })
  }

  // 测试数据库连接
  static async testDatabase(): Promise<ApiResponse<{
    status: 'success' | 'failed'
    message: string
    responseTime: number
  }>> {
    return api.post('/system/test-database', {}, {
      showLoading: true,
    })
  }

  // 获取系统监控数据
  static async getMonitoringData(timeRange: '1h' | '24h' | '7d' | '30d' = '24h'): Promise<ApiResponse<{
    cpu: Array<{ timestamp: string; value: number }>
    memory: Array<{ timestamp: string; value: number }>
    disk: Array<{ timestamp: string; value: number }>
    network: Array<{ timestamp: string; in: number; out: number }>
    requests: Array<{ timestamp: string; count: number; errors: number }>
  }>> {
    return api.get('/system/monitoring', { timeRange }, {
      showLoading: false,
    })
  }

  // 重启系统服务
  static async restartService(serviceName: string): Promise<ApiResponse<{ message: string }>> {
    return api.post(`/system/services/${serviceName}/restart`, {}, {
      showLoading: true,
      showSuccess: true,
      successMessage: `服务 ${serviceName} 重启成功`,
    })
  }

  // 获取系统服务状态
  static async getServicesStatus(): Promise<ApiResponse<Record<string, {
    status: 'running' | 'stopped' | 'error'
    uptime: number
    memory: number
    cpu: number
  }>>> {
    return api.get('/system/services', {}, {
      showLoading: false,
    })
  }
}

// 导出便捷方法
export const {
  getHealth,
  getConfig,
  updateConfig,
  getStats,
  getVersion,
  getLogs,
  clearCache,
  createBackup,
  getBackups,
  downloadBackup,
  deleteBackup,
  enableMaintenanceMode,
  disableMaintenanceMode,
  getMaintenanceStatus,
  sendTestEmail,
  testStorage,
  testDatabase,
  getMonitoringData,
  restartService,
  getServicesStatus,
} = SystemAPI
