/**
 * 文件相关 API 端点
 */

import { api } from '../client'
import type { ApiResponse, PaginatedResponse } from '../types'

// 文件相关类型定义
export interface FileInfo {
  id: string
  name: string
  originalName: string
  size: number
  type: string
  url: string
  thumbnailUrl?: string
  uploadedBy: string
  uploadedAt: string
  tags?: string[]
  metadata?: Record<string, any>
}

export interface FileListParams {
  page?: number
  pageSize?: number
  search?: string
  type?: string
  uploadedBy?: string
  tags?: string[]
  sortBy?: 'name' | 'size' | 'uploadedAt'
  sortOrder?: 'asc' | 'desc'
}

// 文件 API 类
export class FilesAPI {
  // 获取文件列表
  static async getFiles(params?: FileListParams): Promise<PaginatedResponse<FileInfo>> {
    return api.get('/files', params, {
      showLoading: false,
    })
  }

  // 获取文件详情
  static async getFile(id: string): Promise<ApiResponse<FileInfo>> {
    return api.get(`/files/${id}`, {}, {
      showLoading: false,
    })
  }

  // 上传文件
  static async uploadFile(
    file: File,
    options?: {
      tags?: string[]
      metadata?: Record<string, any>
      onProgress?: (progress: number) => void
    }
  ): Promise<ApiResponse<FileInfo>> {
    const formData = new FormData()
    formData.append('file', file)
    
    if (options?.tags) {
      formData.append('tags', JSON.stringify(options.tags))
    }
    
    if (options?.metadata) {
      formData.append('metadata', JSON.stringify(options.metadata))
    }

    return api.upload('/files/upload', formData, {
      showLoading: true,
      showSuccess: true,
      successMessage: '文件上传成功',
      onProgress: options?.onProgress,
    })
  }

  // 批量上传文件
  static async uploadFiles(
    files: FileList | File[],
    options?: {
      tags?: string[]
      metadata?: Record<string, any>
      onProgress?: (progress: number) => void
    }
  ): Promise<ApiResponse<FileInfo[]>> {
    const formData = new FormData()
    
    Array.from(files).forEach((file, index) => {
      formData.append(`files[${index}]`, file)
    })
    
    if (options?.tags) {
      formData.append('tags', JSON.stringify(options.tags))
    }
    
    if (options?.metadata) {
      formData.append('metadata', JSON.stringify(options.metadata))
    }

    return api.upload('/files/batch-upload', formData, {
      showLoading: true,
      showSuccess: true,
      successMessage: '文件批量上传成功',
      onProgress: options?.onProgress,
    })
  }

  // 下载文件
  static async downloadFile(id: string, filename?: string): Promise<Blob> {
    return api.download(`/files/${id}/download`, {
      filename,
      showLoading: true,
      showSuccess: true,
      successMessage: '文件下载完成',
    })
  }

  // 删除文件
  static async deleteFile(id: string): Promise<ApiResponse<{ message: string }>> {
    return api.delete(`/files/${id}`, {
      showLoading: true,
      showSuccess: true,
      successMessage: '文件删除成功',
    })
  }

  // 批量删除文件
  static async deleteFiles(ids: string[]): Promise<ApiResponse<{ message: string; deletedCount: number }>> {
    return api.post('/files/batch-delete', { ids }, {
      showLoading: true,
      showSuccess: true,
      successMessage: '文件批量删除成功',
    })
  }

  // 更新文件信息
  static async updateFile(
    id: string,
    data: {
      name?: string
      tags?: string[]
      metadata?: Record<string, any>
    }
  ): Promise<ApiResponse<FileInfo>> {
    return api.put(`/files/${id}`, data, {
      showLoading: true,
      showSuccess: true,
      successMessage: '文件信息更新成功',
    })
  }

  // 获取文件统计
  static async getFileStats(): Promise<ApiResponse<{
    total: number
    totalSize: number
    typeDistribution: Record<string, number>
    uploadTrend: Array<{ date: string; count: number; size: number }>
  }>> {
    return api.get('/files/stats', {}, {
      showLoading: false,
    })
  }

  // 搜索文件
  static async searchFiles(query: string, limit: number = 10): Promise<ApiResponse<FileInfo[]>> {
    return api.get('/files/search', { q: query, limit }, {
      showLoading: false,
      showError: false,
    })
  }

  // 获取文件标签
  static async getFileTags(): Promise<ApiResponse<string[]>> {
    return api.get('/files/tags', {}, {
      showLoading: false,
    })
  }

  // 按标签获取文件
  static async getFilesByTag(tag: string, params?: FileListParams): Promise<PaginatedResponse<FileInfo>> {
    return api.get('/files/by-tag', { tag, ...params }, {
      showLoading: false,
    })
  }

  // 生成文件分享链接
  static async generateShareLink(
    id: string,
    options?: {
      expiresIn?: number // 过期时间（秒）
      password?: string
      downloadLimit?: number
    }
  ): Promise<ApiResponse<{
    shareUrl: string
    expiresAt?: string
    password?: string
  }>> {
    return api.post(`/files/${id}/share`, options, {
      showLoading: true,
      showSuccess: true,
      successMessage: '分享链接生成成功',
    })
  }

  // 取消文件分享
  static async revokeShareLink(id: string): Promise<ApiResponse<{ message: string }>> {
    return api.delete(`/files/${id}/share`, {
      showLoading: true,
      showSuccess: true,
      successMessage: '分享链接已取消',
    })
  }

  // 获取文件分享信息
  static async getShareInfo(shareToken: string): Promise<ApiResponse<{
    file: FileInfo
    expiresAt?: string
    downloadCount: number
    downloadLimit?: number
    requirePassword: boolean
  }>> {
    return api.get(`/files/share/${shareToken}`, {}, {
      requireAuth: false,
      showLoading: false,
    })
  }

  // 通过分享链接下载文件
  static async downloadSharedFile(
    shareToken: string,
    password?: string
  ): Promise<Blob> {
    return api.download(`/files/share/${shareToken}/download`, {
      data: password ? { password } : undefined,
      requireAuth: false,
      showLoading: true,
      showSuccess: true,
      successMessage: '文件下载完成',
    })
  }
}

// 导出便捷方法
export const {
  getFiles,
  getFile,
  uploadFile,
  uploadFiles,
  downloadFile,
  deleteFile,
  deleteFiles,
  updateFile,
  getFileStats,
  searchFiles,
  getFileTags,
  getFilesByTag,
  generateShareLink,
  revokeShareLink,
  getShareInfo,
  downloadSharedFile,
} = FilesAPI
