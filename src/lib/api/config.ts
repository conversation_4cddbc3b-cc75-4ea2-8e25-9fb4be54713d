/**
 * API 配置管理
 * 环境变量配置和默认设置
 */

import type { ApiConfig, EnvironmentConfig, SecurityConfig, MonitoringConfig, MockConfig } from './types'

// 获取环境变量
const getEnvVar = (key: string, defaultValue: string = ''): string => {
  return import.meta.env[key] || defaultValue
}

// 获取当前环境
const getCurrentEnvironment = (): 'development' | 'production' | 'test' => {
  const mode = import.meta.env.MODE
  if (mode === 'production') return 'production'
  if (mode === 'test') return 'test'
  return 'development'
}

// 环境配置
const environmentConfig: EnvironmentConfig = {
  development: {
    baseURL: getEnvVar('VITE_API_BASE_URL', 'http://localhost:3001/api'),
    timeout: 30000,
    enableLogging: true,
  },
  production: {
    baseURL: getEnvVar('VITE_API_BASE_URL', 'https://api.example.com'),
    timeout: 15000,
    enableLogging: false,
  },
  test: {
    baseURL: getEnvVar('VITE_API_BASE_URL', 'http://localhost:3001/api'),
    timeout: 10000,
    enableLogging: false,
  },
}

// 安全配置
const securityConfig: SecurityConfig = {
  xssProtection: true,
  csrfProtection: true,
  requestSigning: false,
  encryption: {
    enabled: false,
    algorithm: 'AES-256-GCM',
    key: getEnvVar('VITE_ENCRYPTION_KEY', ''),
    iv: getEnvVar('VITE_ENCRYPTION_IV', ''),
  },
}

// 监控配置
const monitoringConfig: MonitoringConfig = {
  performance: getCurrentEnvironment() === 'production',
  errorTracking: true,
  userTracking: getCurrentEnvironment() === 'production',
  sampleRate: getCurrentEnvironment() === 'production' ? 0.1 : 1.0,
}

// Mock 配置
const mockConfig: MockConfig = {
  enabled: getEnvVar('VITE_ENABLE_MOCK', 'false') === 'true',
  delay: 1000,
  errorRate: 0.1,
  endpoints: {},
}

// 当前环境配置
const currentEnv = getCurrentEnvironment()
const currentEnvConfig = environmentConfig[currentEnv]

// 完整的 API 配置
export const apiConfig: ApiConfig = {
  client: {
    baseURL: currentEnvConfig.baseURL,
    timeout: currentEnvConfig.timeout,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'X-Requested-With': 'XMLHttpRequest',
    },
    withCredentials: true,
    enableLogging: currentEnvConfig.enableLogging,
    enableRetry: true,
    defaultRetryCount: 3,
    retryDelay: 1000,
    enableDedupe: true,
    dedupeTime: 5000,
  },
  environment: environmentConfig,
  security: securityConfig,
  monitoring: monitoringConfig,
  mock: mockConfig,
}

// 导出常用配置
export const { client: clientConfig } = apiConfig

// 错误码映射
export const ERROR_CODES = {
  // HTTP 错误码
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504,

  // 业务错误码
  BUSINESS_ERROR: 'BUSINESS_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR: 'AUTHORIZATION_ERROR',
  RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',
  RESOURCE_CONFLICT: 'RESOURCE_CONFLICT',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',

  // 网络错误码
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT: 'TIMEOUT',
  CANCELLED: 'CANCELLED',
  CONNECTION_ERROR: 'CONNECTION_ERROR',
} as const

// 错误消息映射
export const ERROR_MESSAGES = {
  [ERROR_CODES.BAD_REQUEST]: 'api.errors.badRequest',
  [ERROR_CODES.UNAUTHORIZED]: 'api.errors.unauthorized',
  [ERROR_CODES.FORBIDDEN]: 'api.errors.forbidden',
  [ERROR_CODES.NOT_FOUND]: 'api.errors.notFound',
  [ERROR_CODES.METHOD_NOT_ALLOWED]: 'api.errors.methodNotAllowed',
  [ERROR_CODES.CONFLICT]: 'api.errors.conflict',
  [ERROR_CODES.UNPROCESSABLE_ENTITY]: 'api.errors.validationError',
  [ERROR_CODES.TOO_MANY_REQUESTS]: 'api.errors.tooManyRequests',
  [ERROR_CODES.INTERNAL_SERVER_ERROR]: 'api.errors.internalServerError',
  [ERROR_CODES.BAD_GATEWAY]: 'api.errors.badGateway',
  [ERROR_CODES.SERVICE_UNAVAILABLE]: 'api.errors.serviceUnavailable',
  [ERROR_CODES.GATEWAY_TIMEOUT]: 'api.errors.gatewayTimeout',
  [ERROR_CODES.BUSINESS_ERROR]: 'api.errors.businessError',
  [ERROR_CODES.VALIDATION_ERROR]: 'api.errors.validationError',
  [ERROR_CODES.AUTHENTICATION_ERROR]: 'api.errors.authenticationError',
  [ERROR_CODES.AUTHORIZATION_ERROR]: 'api.errors.authorizationError',
  [ERROR_CODES.RESOURCE_NOT_FOUND]: 'api.errors.resourceNotFound',
  [ERROR_CODES.RESOURCE_CONFLICT]: 'api.errors.resourceConflict',
  [ERROR_CODES.RATE_LIMIT_EXCEEDED]: 'api.errors.rateLimitExceeded',
  [ERROR_CODES.NETWORK_ERROR]: 'api.errors.networkError',
  [ERROR_CODES.TIMEOUT]: 'api.errors.timeout',
  [ERROR_CODES.CANCELLED]: 'api.errors.cancelled',
  [ERROR_CODES.CONNECTION_ERROR]: 'api.errors.connectionError',
} as const

// API 端点配置
export const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    LOGIN: '/auth/login',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    PROFILE: '/auth/profile',
    REGISTER: '/auth/register',
    FORGOT_PASSWORD: '/auth/forgot-password',
    RESET_PASSWORD: '/auth/reset-password',
    VERIFY_EMAIL: '/auth/verify-email',
  },

  // 用户相关
  USERS: {
    LIST: '/users',
    DETAIL: '/users/:id',
    CREATE: '/users',
    UPDATE: '/users/:id',
    DELETE: '/users/:id',
    AVATAR: '/users/:id/avatar',
  },

  // 文件相关
  FILES: {
    UPLOAD: '/files/upload',
    DOWNLOAD: '/files/:id/download',
    DELETE: '/files/:id',
    LIST: '/files',
  },

  // 系统相关
  SYSTEM: {
    HEALTH: '/system/health',
    VERSION: '/system/version',
    CONFIG: '/system/config',
  },
} as const

// 请求头常量
export const REQUEST_HEADERS = {
  AUTHORIZATION: 'Authorization',
  CONTENT_TYPE: 'Content-Type',
  ACCEPT: 'Accept',
  X_REQUESTED_WITH: 'X-Requested-With',
  X_CSRF_TOKEN: 'X-CSRF-Token',
  X_API_KEY: 'X-API-Key',
  X_REQUEST_ID: 'X-Request-ID',
  X_CLIENT_VERSION: 'X-Client-Version',
  X_DEVICE_ID: 'X-Device-ID',
} as const

// 内容类型常量
export const CONTENT_TYPES = {
  JSON: 'application/json',
  FORM_DATA: 'multipart/form-data',
  FORM_URLENCODED: 'application/x-www-form-urlencoded',
  TEXT: 'text/plain',
  HTML: 'text/html',
  XML: 'application/xml',
} as const

// 缓存键前缀
export const CACHE_KEYS = {
  API_RESPONSE: 'api_response',
  USER_DATA: 'user_data',
  AUTH_TOKEN: 'auth_token',
  CSRF_TOKEN: 'csrf_token',
  REQUEST_DEDUPE: 'request_dedupe',
} as const

// 存储键常量
export const STORAGE_KEYS = {
  ACCESS_TOKEN: 'access_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_DATA: 'user_data',
  CSRF_TOKEN: 'csrf_token',
  API_CACHE: 'api_cache',
  REQUEST_STATS: 'request_stats',
} as const

// 默认配置值
export const DEFAULT_CONFIG = {
  TIMEOUT: 15000,
  RETRY_COUNT: 3,
  RETRY_DELAY: 1000,
  CACHE_TIME: 5 * 60 * 1000, // 5分钟
  DEDUPE_TIME: 5000, // 5秒
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  CHUNK_SIZE: 1024 * 1024, // 1MB
} as const

// 导出类型
export type ErrorCode = keyof typeof ERROR_CODES
export type ErrorMessage = keyof typeof ERROR_MESSAGES
export type ApiEndpoint = typeof API_ENDPOINTS
export type RequestHeader = typeof REQUEST_HEADERS
export type ContentType = typeof CONTENT_TYPES
