/**
 * API 系统测试
 * 验证 API 系统的基本功能
 */

import { api, getRequestStats, resetRequestStats } from './index'

// 测试基础 API 调用
export async function testBasicAPI() {
  console.log('🧪 Testing Basic API...')
  
  try {
    // 重置统计
    resetRequestStats()
    
    // 测试 GET 请求
    console.log('Testing GET request...')
    const response = await api.get('/test', { param: 'value' }, {
      showLoading: false,
      showError: false,
    })
    console.log('✅ GET request successful')
    
    // 测试 POST 请求
    console.log('Testing POST request...')
    await api.post('/test', { data: 'test' }, {
      showLoading: false,
      showError: false,
    })
    console.log('✅ POST request successful')
    
    // 获取统计信息
    const stats = getRequestStats()
    console.log('📊 Request Stats:', stats)
    
    console.log('✅ All API tests passed!')
    return true
  } catch (error) {
    console.error('❌ API test failed:', error)
    return false
  }
}

// 测试错误处理
export async function testErrorHandling() {
  console.log('🧪 Testing Error Handling...')
  
  try {
    // 测试 404 错误
    await api.get('/non-existent-endpoint', {}, {
      showLoading: false,
      showError: false,
    })
  } catch (error) {
    console.log('✅ 404 error handled correctly:', error)
  }
  
  try {
    // 测试网络错误
    await api.get('http://invalid-domain.test', {}, {
      showLoading: false,
      showError: false,
    })
  } catch (error) {
    console.log('✅ Network error handled correctly:', error)
  }
  
  console.log('✅ Error handling tests passed!')
  return true
}

// 运行所有测试
export async function runAPITests() {
  console.log('🚀 Starting API System Tests...')
  
  const results = await Promise.allSettled([
    testBasicAPI(),
    testErrorHandling(),
  ])
  
  const passed = results.filter(r => r.status === 'fulfilled' && r.value === true).length
  const total = results.length
  
  console.log(`📊 Test Results: ${passed}/${total} tests passed`)
  
  if (passed === total) {
    console.log('🎉 All API tests passed!')
  } else {
    console.log('⚠️ Some API tests failed')
  }
  
  return passed === total
}

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined' && window.location?.search?.includes('test=api')) {
  runAPITests()
}
