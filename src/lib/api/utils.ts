/**
 * API 工具函数
 * 提供各种辅助功能
 */

import { clsx } from 'clsx'
import type { 
  ApiResponse, 
  ApiError, 
  CacheItem, 
  RequestStats,
  ApiRequestConfig 
} from './types'
import { CACHE_KEYS, STORAGE_KEYS } from './config'

// 生成唯一请求 ID
export const generateRequestId = (): string => {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

// 生成请求签名
export const generateRequestSignature = (
  method: string,
  url: string,
  data?: any,
  timestamp?: number
): string => {
  const ts = timestamp || Date.now()
  const payload = JSON.stringify({ method, url, data, timestamp: ts })
  // 这里应该使用真实的签名算法，比如 HMAC-SHA256
  return btoa(payload)
}

// URL 参数处理
export const buildUrl = (baseUrl: string, params?: Record<string, any>): string => {
  if (!params || Object.keys(params).length === 0) {
    return baseUrl
  }

  const url = new URL(baseUrl, window.location.origin)
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      url.searchParams.append(key, String(value))
    }
  })

  return url.toString().replace(window.location.origin, '')
}

// 路径参数替换
export const replacePath = (path: string, params: Record<string, string | number>): string => {
  let result = path
  Object.entries(params).forEach(([key, value]) => {
    result = result.replace(`:${key}`, String(value))
  })
  return result
}

// 数据序列化
export const serializeData = (data: any): string => {
  if (typeof data === 'string') return data
  if (data instanceof FormData) return '[FormData]'
  if (data instanceof File) return `[File: ${data.name}]`
  
  try {
    return JSON.stringify(data, null, 2)
  } catch {
    return '[Unserializable Data]'
  }
}

// XSS 防护 - 清理用户输入
export const sanitizeInput = (input: any): any => {
  if (typeof input === 'string') {
    return input
      .replace(/[<>]/g, '') // 移除尖括号
      .replace(/javascript:/gi, '') // 移除 javascript: 协议
      .replace(/on\w+=/gi, '') // 移除事件处理器
  }
  
  if (Array.isArray(input)) {
    return input.map(sanitizeInput)
  }
  
  if (input && typeof input === 'object') {
    const sanitized: any = {}
    Object.entries(input).forEach(([key, value]) => {
      sanitized[key] = sanitizeInput(value)
    })
    return sanitized
  }
  
  return input
}

// 数据加密（简单示例，实际应使用专业加密库）
export const encryptData = (data: any, key: string): string => {
  // 这里应该使用真实的加密算法
  const jsonData = JSON.stringify(data)
  return btoa(jsonData + key)
}

// 数据解密
export const decryptData = (encryptedData: string, key: string): any => {
  try {
    const decoded = atob(encryptedData)
    const jsonData = decoded.replace(key, '')
    return JSON.parse(jsonData)
  } catch {
    throw new Error('Failed to decrypt data')
  }
}

// 缓存管理
export class CacheManager {
  private static cache = new Map<string, CacheItem>()

  static set<T>(key: string, data: T, ttl: number = 5 * 60 * 1000): void {
    const item: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      expiresAt: Date.now() + ttl,
      key,
    }
    this.cache.set(key, item)
  }

  static get<T>(key: string): T | null {
    const item = this.cache.get(key)
    if (!item) return null

    if (Date.now() > item.expiresAt) {
      this.cache.delete(key)
      return null
    }

    return item.data as T
  }

  static delete(key: string): void {
    this.cache.delete(key)
  }

  static clear(): void {
    this.cache.clear()
  }

  static has(key: string): boolean {
    const item = this.cache.get(key)
    if (!item) return false

    if (Date.now() > item.expiresAt) {
      this.cache.delete(key)
      return false
    }

    return true
  }

  static size(): number {
    return this.cache.size
  }

  static cleanup(): void {
    const now = Date.now()
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiresAt) {
        this.cache.delete(key)
      }
    }
  }
}

// 请求去重管理
export class DedupeManager {
  private static pendingRequests = new Map<string, Promise<any>>()

  static generateKey(method: string, url: string, data?: any): string {
    const payload = { method: method.toUpperCase(), url, data }
    return btoa(JSON.stringify(payload))
  }

  static add(key: string, promise: Promise<any>): Promise<any> {
    this.pendingRequests.set(key, promise)
    
    // 请求完成后清理
    promise.finally(() => {
      this.pendingRequests.delete(key)
    })

    return promise
  }

  static get(key: string): Promise<any> | null {
    return this.pendingRequests.get(key) || null
  }

  static has(key: string): boolean {
    return this.pendingRequests.has(key)
  }

  static clear(): void {
    this.pendingRequests.clear()
  }
}

// 请求统计管理
export class StatsManager {
  private static stats: RequestStats = {
    total: 0,
    success: 0,
    error: 0,
    pending: 0,
    averageTime: 0,
  }

  static increment(type: 'total' | 'success' | 'error' | 'pending'): void {
    this.stats[type]++
    this.save()
  }

  static decrement(type: 'pending'): void {
    if (this.stats[type] > 0) {
      this.stats[type]--
      this.save()
    }
  }

  static updateAverageTime(time: number): void {
    const total = this.stats.success + this.stats.error
    if (total === 0) {
      this.stats.averageTime = time
    } else {
      this.stats.averageTime = (this.stats.averageTime * (total - 1) + time) / total
    }
    this.stats.lastRequestTime = Date.now()
    this.save()
  }

  static getStats(): RequestStats {
    return { ...this.stats }
  }

  static reset(): void {
    this.stats = {
      total: 0,
      success: 0,
      error: 0,
      pending: 0,
      averageTime: 0,
    }
    this.save()
  }

  private static save(): void {
    try {
      localStorage.setItem(STORAGE_KEYS.REQUEST_STATS, JSON.stringify(this.stats))
    } catch {
      // 忽略存储错误
    }
  }

  static load(): void {
    try {
      const saved = localStorage.getItem(STORAGE_KEYS.REQUEST_STATS)
      if (saved) {
        this.stats = { ...this.stats, ...JSON.parse(saved) }
      }
    } catch {
      // 忽略加载错误
    }
  }
}

// 初始化统计
StatsManager.load()

// 错误分类
export const classifyError = (error: any): 'network' | 'http' | 'business' | 'unknown' => {
  if (!error) return 'unknown'
  
  if (error.code === 'NETWORK_ERROR' || error.code === 'TIMEOUT' || error.code === 'CANCELLED') {
    return 'network'
  }
  
  if (error.response && error.response.status) {
    return 'http'
  }
  
  if (error.code && typeof error.code === 'string') {
    return 'business'
  }
  
  return 'unknown'
}

// 重试延迟计算（指数退避）
export const calculateRetryDelay = (attempt: number, baseDelay: number = 1000): number => {
  return Math.min(baseDelay * Math.pow(2, attempt - 1), 30000) // 最大30秒
}

// 判断是否应该重试
export const shouldRetry = (error: any, attempt: number, maxAttempts: number): boolean => {
  if (attempt >= maxAttempts) return false
  
  // 网络错误可以重试
  if (error.code === 'NETWORK_ERROR' || error.code === 'TIMEOUT') {
    return true
  }
  
  // 5xx 服务器错误可以重试
  if (error.response && error.response.status >= 500) {
    return true
  }
  
  // 429 限流错误可以重试
  if (error.response && error.response.status === 429) {
    return true
  }
  
  return false
}

// 格式化文件大小
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
}

// 验证文件类型
export const validateFileType = (file: File, allowedTypes: string[]): boolean => {
  if (allowedTypes.length === 0) return true
  
  return allowedTypes.some(type => {
    if (type.includes('*')) {
      const baseType = type.split('/')[0]
      return file.type.startsWith(baseType)
    }
    return file.type === type
  })
}

// 创建下载链接
export const createDownloadLink = (blob: Blob, filename: string): void => {
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}

// 合并配置
export const mergeConfig = (
  defaultConfig: ApiRequestConfig,
  userConfig?: Partial<ApiRequestConfig>
): ApiRequestConfig => {
  return {
    ...defaultConfig,
    ...userConfig,
  }
}

// 深度克隆
export const deepClone = <T>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T
  if (obj instanceof Array) return obj.map(item => deepClone(item)) as unknown as T
  if (typeof obj === 'object') {
    const cloned = {} as T
    Object.keys(obj).forEach(key => {
      (cloned as any)[key] = deepClone((obj as any)[key])
    })
    return cloned
  }
  return obj
}

// 防抖函数
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

// 节流函数
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let lastTime = 0
  return (...args: Parameters<T>) => {
    const now = Date.now()
    if (now - lastTime >= wait) {
      lastTime = now
      func(...args)
    }
  }
}
