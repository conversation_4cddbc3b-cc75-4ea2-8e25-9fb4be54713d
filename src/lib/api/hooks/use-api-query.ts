/**
 * API Query Hook
 * 基于 TanStack Query 的数据查询 hook
 */

import {
  useQuery,
  useQueryClient,
  type UseQueryOptions,
  type UseQueryResult
} from '@tanstack/react-query'
import { useRouter } from '@tanstack/react-router'
import { useEffect, useState } from 'react'
import { api } from '../client'
import type { ApiResponse, ApiRequestConfig, QueryConfig } from '../types'
import { mergeConfig } from '../utils'

// 默认查询配置
const defaultQueryConfig: QueryConfig = {
  staleTime: 5 * 60 * 1000, // 5分钟
  cacheTime: 10 * 60 * 1000, // 10分钟
  refetchOnWindowFocus: false,
  refetchOnReconnect: true,
  retry: 3,
  retryDelay: 1000,
}

// API Query Hook 选项
export interface UseApiQueryOptions<T = any> extends Omit<UseQueryOptions<ApiResponse<T>>, 'queryFn'> {
  // API 请求配置
  url: string
  params?: Record<string, any>
  config?: ApiRequestConfig

  // 查询配置
  queryConfig?: QueryConfig

  // 是否在路由变化时取消请求
  cancelOnRouteChange?: boolean

  // 依赖项，当依赖项变化时重新请求
  deps?: any[]
}

// API Query Hook
export function useApiQuery<T = any>(
  options: UseApiQueryOptions<T>
): UseQueryResult<ApiResponse<T>> {
  const {
    url,
    params,
    config,
    queryConfig,
    cancelOnRouteChange = true,
    deps = [],
    ...queryOptions
  } = options

  const queryClient = useQueryClient()
  const router = useRouter()

  // 合并配置
  const mergedConfig = mergeConfig(
    {
      showLoading: false, // Query 自己管理 loading 状态
      showError: true,
    },
    config
  )

  const mergedQueryConfig = { ...defaultQueryConfig, ...queryConfig }

  // 生成查询键
  const queryKey = [url, params, deps]

  // 查询函数
  const queryFn = async (): Promise<ApiResponse<T>> => {
    return api.get<T>(url, params, mergedConfig)
  }

  // 使用 useQuery
  const result = useQuery<ApiResponse<T>>({
    queryKey,
    queryFn,
    staleTime: mergedQueryConfig.staleTime,
    cacheTime: mergedQueryConfig.cacheTime,
    refetchOnWindowFocus: mergedQueryConfig.refetchOnWindowFocus,
    refetchOnReconnect: mergedQueryConfig.refetchOnReconnect,
    retry: mergedQueryConfig.retry,
    retryDelay: mergedQueryConfig.retryDelay,
    ...queryOptions,
  })

  // 路由变化时取消请求
  useEffect(() => {
    if (!cancelOnRouteChange) return

    const unsubscribe = router.subscribe('onBeforeLoad', () => {
      queryClient.cancelQueries({ queryKey })
    })

    return unsubscribe
  }, [queryClient, queryKey, router, cancelOnRouteChange])

  return result
}

// 便捷的查询 hooks

// 获取用户信息
export function useUser() {
  return useApiQuery({
    url: '/auth/profile',
    queryKey: ['user'],
    staleTime: 10 * 60 * 1000, // 10分钟
  })
}

// 获取用户列表
export function useUsers(params?: { page?: number; pageSize?: number; search?: string }) {
  return useApiQuery({
    url: '/users',
    params,
    queryKey: ['users', params],
    deps: [params],
  })
}

// 获取用户详情
export function useUserDetail(id: string | number, enabled: boolean = true) {
  return useApiQuery({
    url: `/users/${id}`,
    queryKey: ['user', id],
    enabled: enabled && !!id,
  })
}

// 获取系统配置
export function useSystemConfig() {
  return useApiQuery({
    url: '/system/config',
    queryKey: ['system', 'config'],
    staleTime: 30 * 60 * 1000, // 30分钟
  })
}

// 获取健康检查
export function useHealthCheck() {
  return useApiQuery({
    url: '/system/health',
    queryKey: ['system', 'health'],
    refetchInterval: 30 * 1000, // 30秒轮询
    staleTime: 0, // 总是重新获取
  })
}

// 条件查询 hook
export function useConditionalQuery<T = any>(
  condition: boolean,
  options: UseApiQueryOptions<T>
) {
  return useApiQuery<T>({
    ...options,
    enabled: condition && (options.enabled !== false),
  })
}

// 依赖查询 hook
export function useDependentQuery<T = any>(
  dependency: any,
  options: UseApiQueryOptions<T>
) {
  return useApiQuery<T>({
    ...options,
    enabled: !!dependency && (options.enabled !== false),
    deps: [dependency, ...(options.deps || [])],
  })
}

// 分页查询 hook
export function usePaginatedQuery<T = any>(
  url: string,
  page: number = 1,
  pageSize: number = 10,
  additionalParams?: Record<string, any>,
  config?: ApiRequestConfig
) {
  const params = {
    page,
    pageSize,
    ...additionalParams,
  }

  return useApiQuery<{
    items: T[]
    total: number
    page: number
    pageSize: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }>({
    url,
    params,
    config,
    queryKey: [url, 'paginated', params],
    deps: [page, pageSize, additionalParams],
  })
}

// 搜索查询 hook
export function useSearchQuery<T = any>(
  url: string,
  searchTerm: string,
  debounceMs: number = 300,
  config?: ApiRequestConfig
) {
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(searchTerm)

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm)
    }, debounceMs)

    return () => clearTimeout(timer)
  }, [searchTerm, debounceMs])

  return useApiQuery<T[]>({
    url,
    params: { search: debouncedSearchTerm },
    config,
    queryKey: [url, 'search', debouncedSearchTerm],
    enabled: debouncedSearchTerm.length > 0,
    deps: [debouncedSearchTerm],
  })
}

// 实时查询 hook（轮询）
export function useRealtimeQuery<T = any>(
  options: UseApiQueryOptions<T>,
  interval: number = 5000
) {
  return useApiQuery<T>({
    ...options,
    refetchInterval: interval,
    refetchIntervalInBackground: true,
  })
}

// 缓存查询 hook
export function useCachedQuery<T = any>(
  options: UseApiQueryOptions<T>,
  cacheTime: number = 30 * 60 * 1000 // 30分钟
) {
  return useApiQuery<T>({
    ...options,
    queryConfig: {
      ...options.queryConfig,
      staleTime: cacheTime,
      cacheTime: cacheTime * 2,
    },
  })
}

// 预加载查询
export function usePrefetchQuery() {
  const queryClient = useQueryClient()

  const prefetch = async <T = any>(
    url: string,
    params?: Record<string, any>,
    config?: ApiRequestConfig
  ) => {
    await queryClient.prefetchQuery({
      queryKey: [url, params],
      queryFn: () => api.get<T>(url, params, config),
      staleTime: 5 * 60 * 1000,
    })
  }

  return { prefetch }
}

// 查询缓存操作
export function useQueryCache() {
  const queryClient = useQueryClient()

  const invalidate = (queryKey: any[]) => {
    queryClient.invalidateQueries({ queryKey })
  }

  const remove = (queryKey: any[]) => {
    queryClient.removeQueries({ queryKey })
  }

  const setData = <T = any>(queryKey: any[], data: T) => {
    queryClient.setQueryData(queryKey, data)
  }

  const getData = <T = any>(queryKey: any[]): T | undefined => {
    return queryClient.getQueryData<T>(queryKey)
  }

  return {
    invalidate,
    remove,
    setData,
    getData,
  }
}
