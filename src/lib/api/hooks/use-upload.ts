/**
 * 文件上传 Hook
 * 提供文件上传功能和进度管理
 */

import { useState, useCallback } from 'react'
import { useMutation, type UseMutationResult } from '@tanstack/react-query'
import { toast } from 'sonner'
import { api } from '../client'
import type { ApiResponse, UploadConfig } from '../types'
import { formatFileSize, validateFileType } from '../utils'

// 上传状态
export interface UploadState {
  progress: number
  isUploading: boolean
  error: string | null
  uploadedFiles: UploadedFile[]
}

// 上传文件信息
export interface UploadedFile {
  id: string
  name: string
  size: number
  type: string
  url: string
  thumbnailUrl?: string
}

// 上传 Hook 选项
export interface UseUploadOptions extends UploadConfig {
  // 上传端点
  endpoint?: string
  
  // 是否自动上传
  autoUpload?: boolean
  
  // 上传前的文件处理
  beforeUpload?: (file: File) => boolean | Promise<boolean>
  
  // 上传成功回调
  onUploadSuccess?: (file: UploadedFile, response: ApiResponse<UploadedFile>) => void
  
  // 上传失败回调
  onUploadError?: (file: File, error: any) => void
  
  // 所有文件上传完成回调
  onAllUploadsComplete?: (files: UploadedFile[]) => void
}

// 文件上传 Hook
export function useUpload(options: UseUploadOptions = {}) {
  const {
    endpoint = '/files/upload',
    autoUpload = true,
    beforeUpload,
    onUploadSuccess,
    onUploadError,
    onAllUploadsComplete,
    accept = [],
    maxSize = 10 * 1024 * 1024, // 10MB
    multiple = false,
    showLoading = false,
    showError = true,
    showSuccess = true,
    ...config
  } = options

  // 上传状态
  const [uploadState, setUploadState] = useState<UploadState>({
    progress: 0,
    isUploading: false,
    error: null,
    uploadedFiles: [],
  })

  // 上传 Mutation
  const uploadMutation = useMutation<ApiResponse<UploadedFile>, Error, File>({
    mutationFn: async (file: File) => {
      // 文件验证
      if (accept.length > 0 && !validateFileType(file, accept)) {
        throw new Error(`不支持的文件类型: ${file.type}`)
      }

      if (file.size > maxSize) {
        throw new Error(`文件大小超过限制: ${formatFileSize(file.size)} > ${formatFileSize(maxSize)}`)
      }

      // 上传前处理
      if (beforeUpload) {
        const canUpload = await beforeUpload(file)
        if (!canUpload) {
          throw new Error('上传被取消')
        }
      }

      // 执行上传
      return api.upload<UploadedFile>(endpoint, file, {
        ...config,
        showLoading,
        showError,
        showSuccess,
        onProgress: (progress) => {
          setUploadState(prev => ({ ...prev, progress }))
          options.onProgress?.(progress)
        },
      })
    },
    onMutate: () => {
      setUploadState(prev => ({
        ...prev,
        isUploading: true,
        error: null,
        progress: 0,
      }))
    },
    onSuccess: (response, file) => {
      const uploadedFile = response.data
      
      setUploadState(prev => ({
        ...prev,
        uploadedFiles: [...prev.uploadedFiles, uploadedFile],
        progress: 100,
      }))

      onUploadSuccess?.(uploadedFile, response)

      if (showSuccess) {
        toast.success(`文件 "${file.name}" 上传成功`)
      }
    },
    onError: (error, file) => {
      setUploadState(prev => ({
        ...prev,
        error: error.message,
        progress: 0,
      }))

      onUploadError?.(file, error)

      if (showError) {
        toast.error(`文件 "${file.name}" 上传失败: ${error.message}`)
      }
    },
    onSettled: () => {
      setUploadState(prev => ({ ...prev, isUploading: false }))
    },
  })

  // 批量上传 Mutation
  const batchUploadMutation = useMutation<UploadedFile[], Error, FileList | File[]>({
    mutationFn: async (files: FileList | File[]) => {
      const fileArray = Array.from(files)
      const uploadedFiles: UploadedFile[] = []
      
      for (let i = 0; i < fileArray.length; i++) {
        const file = fileArray[i]
        
        try {
          const response = await uploadMutation.mutateAsync(file)
          uploadedFiles.push(response.data)
        } catch (error) {
          console.error(`Failed to upload file ${file.name}:`, error)
          // 继续上传其他文件
        }
        
        // 更新总体进度
        const overallProgress = ((i + 1) / fileArray.length) * 100
        setUploadState(prev => ({ ...prev, progress: overallProgress }))
      }
      
      return uploadedFiles
    },
    onMutate: () => {
      setUploadState(prev => ({
        ...prev,
        isUploading: true,
        error: null,
        progress: 0,
        uploadedFiles: [],
      }))
    },
    onSuccess: (uploadedFiles) => {
      setUploadState(prev => ({
        ...prev,
        uploadedFiles,
        progress: 100,
      }))

      onAllUploadsComplete?.(uploadedFiles)

      if (showSuccess) {
        toast.success(`成功上传 ${uploadedFiles.length} 个文件`)
      }
    },
    onError: (error) => {
      setUploadState(prev => ({
        ...prev,
        error: error.message,
      }))

      if (showError) {
        toast.error(`批量上传失败: ${error.message}`)
      }
    },
    onSettled: () => {
      setUploadState(prev => ({ ...prev, isUploading: false }))
    },
  })

  // 上传单个文件
  const uploadFile = useCallback((file: File) => {
    if (autoUpload) {
      uploadMutation.mutate(file)
    }
    return uploadMutation
  }, [uploadMutation, autoUpload])

  // 上传多个文件
  const uploadFiles = useCallback((files: FileList | File[]) => {
    if (autoUpload) {
      batchUploadMutation.mutate(files)
    }
    return batchUploadMutation
  }, [batchUploadMutation, autoUpload])

  // 手动触发上传
  const triggerUpload = useCallback((files: File | FileList | File[]) => {
    if (files instanceof File) {
      uploadMutation.mutate(files)
    } else {
      batchUploadMutation.mutate(files)
    }
  }, [uploadMutation, batchUploadMutation])

  // 重置状态
  const reset = useCallback(() => {
    setUploadState({
      progress: 0,
      isUploading: false,
      error: null,
      uploadedFiles: [],
    })
    uploadMutation.reset()
    batchUploadMutation.reset()
  }, [uploadMutation, batchUploadMutation])

  // 移除已上传的文件
  const removeUploadedFile = useCallback((fileId: string) => {
    setUploadState(prev => ({
      ...prev,
      uploadedFiles: prev.uploadedFiles.filter(file => file.id !== fileId),
    }))
  }, [])

  return {
    // 状态
    ...uploadState,
    
    // 方法
    uploadFile,
    uploadFiles,
    triggerUpload,
    reset,
    removeUploadedFile,
    
    // Mutation 对象
    uploadMutation,
    batchUploadMutation,
  }
}

// 拖拽上传 Hook
export function useDragUpload(options: UseUploadOptions = {}) {
  const upload = useUpload(options)
  const [isDragOver, setIsDragOver] = useState(false)

  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragOver(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragOver(false)
  }, [])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragOver(false)

    const files = e.dataTransfer.files
    if (files.length > 0) {
      if (options.multiple || files.length === 1) {
        upload.uploadFiles(files)
      } else {
        upload.uploadFile(files[0])
      }
    }
  }, [upload, options.multiple])

  const dragProps = {
    onDragEnter: handleDragEnter,
    onDragLeave: handleDragLeave,
    onDragOver: handleDragOver,
    onDrop: handleDrop,
  }

  return {
    ...upload,
    isDragOver,
    dragProps,
  }
}

// 图片上传 Hook（带预览）
export function useImageUpload(options: UseUploadOptions = {}) {
  const [previews, setPreviews] = useState<string[]>([])

  const upload = useUpload({
    ...options,
    accept: options.accept?.length ? options.accept : ['image/*'],
    beforeUpload: async (file) => {
      // 生成预览
      const preview = URL.createObjectURL(file)
      setPreviews(prev => [...prev, preview])

      // 执行原始的 beforeUpload
      if (options.beforeUpload) {
        return options.beforeUpload(file)
      }
      return true
    },
  })

  const clearPreviews = useCallback(() => {
    previews.forEach(URL.revokeObjectURL)
    setPreviews([])
  }, [previews])

  const removePreview = useCallback((index: number) => {
    const preview = previews[index]
    if (preview) {
      URL.revokeObjectURL(preview)
      setPreviews(prev => prev.filter((_, i) => i !== index))
    }
  }, [previews])

  return {
    ...upload,
    previews,
    clearPreviews,
    removePreview,
  }
}

// 分片上传 Hook（大文件）
export function useChunkUpload(options: UseUploadOptions & { chunkSize?: number } = {}) {
  const { chunkSize = 1024 * 1024, ...uploadOptions } = options // 1MB chunks
  const [chunks, setChunks] = useState<{ uploaded: number; total: number }>({ uploaded: 0, total: 0 })

  const chunkUploadMutation = useMutation<ApiResponse<UploadedFile>, Error, File>({
    mutationFn: async (file: File) => {
      const totalChunks = Math.ceil(file.size / chunkSize)
      setChunks({ uploaded: 0, total: totalChunks })

      const uploadId = `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      
      // 上传分片
      for (let i = 0; i < totalChunks; i++) {
        const start = i * chunkSize
        const end = Math.min(start + chunkSize, file.size)
        const chunk = file.slice(start, end)
        
        const formData = new FormData()
        formData.append('chunk', chunk)
        formData.append('chunkIndex', i.toString())
        formData.append('totalChunks', totalChunks.toString())
        formData.append('uploadId', uploadId)
        formData.append('filename', file.name)
        
        await api.post('/files/upload-chunk', formData, {
          ...uploadOptions,
          showLoading: false,
        })
        
        setChunks(prev => ({ ...prev, uploaded: i + 1 }))
        
        // 更新进度
        const progress = ((i + 1) / totalChunks) * 100
        uploadOptions.onProgress?.(progress)
      }
      
      // 合并分片
      return api.post<UploadedFile>('/files/merge-chunks', {
        uploadId,
        filename: file.name,
        totalChunks,
      }, uploadOptions)
    },
  })

  return {
    ...chunkUploadMutation,
    chunks,
    uploadFile: chunkUploadMutation.mutate,
  }
}
