/**
 * API Mutation Hook
 * 基于 TanStack Query 的数据变更 hook
 */

import { 
  useMutation, 
  useQueryClient,
  type UseMutationOptions,
  type UseMutationResult 
} from '@tanstack/react-query'
import { toast } from 'sonner'
import { api } from '../client'
import type { ApiResponse, ApiRequestConfig, MutationConfig } from '../types'
import { mergeConfig } from '../utils'

// 默认 Mutation 配置
const defaultMutationConfig: MutationConfig = {
  retry: 1,
  retryDelay: 1000,
}

// API Mutation Hook 选项
export interface UseApiMutationOptions<TData = any, TVariables = any> 
  extends Omit<UseMutationOptions<ApiResponse<TData>, Error, TVariables>, 'mutationFn'> {
  // API 请求配置
  config?: ApiRequestConfig
  
  // Mutation 配置
  mutationConfig?: MutationConfig
  
  // 成功后要失效的查询键
  invalidateQueries?: any[][]
  
  // 成功后要更新的查询数据
  updateQueries?: Array<{
    queryKey: any[]
    updater: (oldData: any, newData: TData) => any
  }>
  
  // 是否显示成功提示
  showSuccessToast?: boolean
  
  // 成功提示消息
  successMessage?: string
}

// API Mutation Hook
export function useApiMutation<TData = any, TVariables = any>(
  mutationFn: (variables: TVariables) => Promise<ApiResponse<TData>>,
  options: UseApiMutationOptions<TData, TVariables> = {}
): UseMutationResult<ApiResponse<TData>, Error, TVariables> {
  const {
    config,
    mutationConfig,
    invalidateQueries = [],
    updateQueries = [],
    showSuccessToast = true,
    successMessage,
    onSuccess,
    onError,
    ...mutationOptions
  } = options

  const queryClient = useQueryClient()

  // 合并配置
  const mergedConfig = mergeConfig(
    { 
      showLoading: true,
      showError: true,
    },
    config
  )

  const mergedMutationConfig = { ...defaultMutationConfig, ...mutationConfig }

  return useMutation<ApiResponse<TData>, Error, TVariables>({
    mutationFn,
    retry: mergedMutationConfig.retry,
    retryDelay: mergedMutationConfig.retryDelay,
    onSuccess: (data, variables, context) => {
      // 显示成功提示
      if (showSuccessToast) {
        const message = successMessage || data.message || '操作成功'
        toast.success(message)
      }

      // 失效相关查询
      invalidateQueries.forEach(queryKey => {
        queryClient.invalidateQueries({ queryKey })
      })

      // 更新查询数据
      updateQueries.forEach(({ queryKey, updater }) => {
        queryClient.setQueryData(queryKey, (oldData: any) => {
          return updater(oldData, data.data)
        })
      })

      // 执行自定义成功回调
      onSuccess?.(data, variables, context)
      mergedMutationConfig.onSuccess?.(data)
    },
    onError: (error, variables, context) => {
      // 执行自定义错误回调
      onError?.(error, variables, context)
      mergedMutationConfig.onError?.(error)
    },
    onSettled: (data, error, variables, context) => {
      // 执行自定义完成回调
      mergedMutationConfig.onSettled?.()
    },
    ...mutationOptions,
  })
}

// 便捷的 Mutation Hooks

// POST 请求 Mutation
export function usePostMutation<TData = any, TVariables = any>(
  url: string,
  options: UseApiMutationOptions<TData, TVariables> = {}
) {
  return useApiMutation<TData, TVariables>(
    (variables) => api.post<TData>(url, variables, options.config),
    options
  )
}

// PUT 请求 Mutation
export function usePutMutation<TData = any, TVariables = any>(
  url: string,
  options: UseApiMutationOptions<TData, TVariables> = {}
) {
  return useApiMutation<TData, TVariables>(
    (variables) => api.put<TData>(url, variables, options.config),
    options
  )
}

// PATCH 请求 Mutation
export function usePatchMutation<TData = any, TVariables = any>(
  url: string,
  options: UseApiMutationOptions<TData, TVariables> = {}
) {
  return useApiMutation<TData, TVariables>(
    (variables) => api.patch<TData>(url, variables, options.config),
    options
  )
}

// DELETE 请求 Mutation
export function useDeleteMutation<TData = any>(
  url: string,
  options: UseApiMutationOptions<TData, void> = {}
) {
  return useApiMutation<TData, void>(
    () => api.delete<TData>(url, options.config),
    options
  )
}

// 动态 URL 的 Mutation
export function useDynamicMutation<TData = any, TVariables = any>(
  urlTemplate: string,
  method: 'POST' | 'PUT' | 'PATCH' | 'DELETE' = 'POST',
  options: UseApiMutationOptions<TData, TVariables & { id?: string | number }> = {}
) {
  return useApiMutation<TData, TVariables & { id?: string | number }>(
    (variables) => {
      const { id, ...data } = variables as any
      const url = urlTemplate.replace(':id', String(id))
      
      switch (method) {
        case 'POST':
          return api.post<TData>(url, data, options.config)
        case 'PUT':
          return api.put<TData>(url, data, options.config)
        case 'PATCH':
          return api.patch<TData>(url, data, options.config)
        case 'DELETE':
          return api.delete<TData>(url, options.config)
        default:
          throw new Error(`Unsupported method: ${method}`)
      }
    },
    options
  )
}

// 批量操作 Mutation
export function useBatchMutation<TData = any, TVariables = any>(
  operations: Array<{
    url: string
    method: 'POST' | 'PUT' | 'PATCH' | 'DELETE'
    data?: any
  }>,
  options: UseApiMutationOptions<TData[], TVariables> = {}
) {
  return useApiMutation<TData[], TVariables>(
    async (variables) => {
      const requests = operations.map(op => ({
        method: op.method,
        url: op.url,
        data: op.data || variables,
        ...options.config,
      }))
      
      const results = await api.batch<TData>(requests)
      return {
        success: true,
        data: results.map(r => r.data),
        message: '批量操作完成',
      }
    },
    options
  )
}

// 乐观更新 Mutation
export function useOptimisticMutation<TData = any, TVariables = any>(
  mutationFn: (variables: TVariables) => Promise<ApiResponse<TData>>,
  optimisticUpdater: (variables: TVariables) => TData,
  queryKey: any[],
  options: UseApiMutationOptions<TData, TVariables> = {}
) {
  const queryClient = useQueryClient()

  return useApiMutation<TData, TVariables>(
    mutationFn,
    {
      ...options,
      onMutate: async (variables) => {
        // 取消正在进行的查询
        await queryClient.cancelQueries({ queryKey })

        // 获取当前数据
        const previousData = queryClient.getQueryData(queryKey)

        // 乐观更新
        queryClient.setQueryData(queryKey, optimisticUpdater(variables))

        // 返回回滚数据
        return { previousData }
      },
      onError: (error, variables, context) => {
        // 回滚数据
        if (context?.previousData) {
          queryClient.setQueryData(queryKey, context.previousData)
        }
        
        options.onError?.(error, variables, context)
      },
      onSettled: () => {
        // 重新获取数据
        queryClient.invalidateQueries({ queryKey })
        options.onSettled?.()
      },
    }
  )
}

// 具体业务 Mutation Hooks

// 用户相关
export function useCreateUser() {
  return usePostMutation('/users', {
    successMessage: '用户创建成功',
    invalidateQueries: [['users']],
  })
}

export function useUpdateUser() {
  return useDynamicMutation('/users/:id', 'PUT', {
    successMessage: '用户更新成功',
    invalidateQueries: [['users'], ['user']],
  })
}

export function useDeleteUser() {
  return useDynamicMutation('/users/:id', 'DELETE', {
    successMessage: '用户删除成功',
    invalidateQueries: [['users']],
  })
}

// 认证相关
export function useLogin() {
  return usePostMutation('/auth/login', {
    successMessage: '登录成功',
    config: { requireAuth: false },
  })
}

export function useLogout() {
  return usePostMutation('/auth/logout', {
    successMessage: '退出成功',
  })
}

export function useRegister() {
  return usePostMutation('/auth/register', {
    successMessage: '注册成功',
    config: { requireAuth: false },
  })
}

export function useChangePassword() {
  return usePostMutation('/auth/change-password', {
    successMessage: '密码修改成功',
  })
}

// 文件相关
export function useDeleteFile() {
  return useDynamicMutation('/files/:id', 'DELETE', {
    successMessage: '文件删除成功',
    invalidateQueries: [['files']],
  })
}
