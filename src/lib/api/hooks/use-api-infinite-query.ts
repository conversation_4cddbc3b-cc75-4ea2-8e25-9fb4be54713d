/**
 * API Infinite Query Hook
 * 基于 TanStack Query 的无限滚动查询 hook
 */

import { 
  useInfiniteQuery,
  type UseInfiniteQueryOptions,
  type UseInfiniteQueryResult 
} from '@tanstack/react-query'
import { api } from '../client'
import type { ApiResponse, ApiRequestConfig, PaginatedResponse } from '../types'
import { mergeConfig } from '../utils'

// 无限查询选项
export interface UseApiInfiniteQueryOptions<T = any> 
  extends Omit<UseInfiniteQueryOptions<PaginatedResponse<T>>, 'queryFn' | 'getNextPageParam' | 'getPreviousPageParam'> {
  // API 请求配置
  url: string
  params?: Record<string, any>
  config?: ApiRequestConfig
  
  // 分页配置
  pageSize?: number
  initialPage?: number
  
  // 自定义分页参数名
  pageParamName?: string
  pageSizeParamName?: string
  
  // 自定义获取下一页参数的函数
  getNextPageParam?: (lastPage: PaginatedResponse<T>, allPages: PaginatedResponse<T>[]) => any
  
  // 自定义获取上一页参数的函数
  getPreviousPageParam?: (firstPage: PaginatedResponse<T>, allPages: PaginatedResponse<T>[]) => any
}

// API 无限查询 Hook
export function useApiInfiniteQuery<T = any>(
  options: UseApiInfiniteQueryOptions<T>
): UseInfiniteQueryResult<PaginatedResponse<T>> {
  const {
    url,
    params = {},
    config,
    pageSize = 20,
    initialPage = 1,
    pageParamName = 'page',
    pageSizeParamName = 'pageSize',
    getNextPageParam,
    getPreviousPageParam,
    ...queryOptions
  } = options

  // 合并配置
  const mergedConfig = mergeConfig(
    { 
      showLoading: false,
      showError: true,
    },
    config
  )

  // 查询函数
  const queryFn = async ({ pageParam = initialPage }): Promise<PaginatedResponse<T>> => {
    const queryParams = {
      ...params,
      [pageParamName]: pageParam,
      [pageSizeParamName]: pageSize,
    }
    
    return api.get<PaginatedResponse<T>['data']>(url, queryParams, mergedConfig)
  }

  // 默认获取下一页参数
  const defaultGetNextPageParam = (lastPage: PaginatedResponse<T>) => {
    return lastPage.data.hasNext ? lastPage.data.page + 1 : undefined
  }

  // 默认获取上一页参数
  const defaultGetPreviousPageParam = (firstPage: PaginatedResponse<T>) => {
    return firstPage.data.hasPrev ? firstPage.data.page - 1 : undefined
  }

  return useInfiniteQuery<PaginatedResponse<T>>({
    queryKey: [url, params, pageSize],
    queryFn,
    getNextPageParam: getNextPageParam || defaultGetNextPageParam,
    getPreviousPageParam: getPreviousPageParam || defaultGetPreviousPageParam,
    ...queryOptions,
  })
}

// 便捷的无限查询 hooks

// 用户列表无限查询
export function useInfiniteUsers(search?: string, pageSize: number = 20) {
  return useApiInfiniteQuery<any>({
    url: '/users',
    params: search ? { search } : {},
    pageSize,
    queryKey: ['users', 'infinite', search],
  })
}

// 文章列表无限查询
export function useInfinitePosts(category?: string, pageSize: number = 10) {
  return useApiInfiniteQuery<any>({
    url: '/posts',
    params: category ? { category } : {},
    pageSize,
    queryKey: ['posts', 'infinite', category],
  })
}

// 评论列表无限查询
export function useInfiniteComments(postId: string | number, pageSize: number = 20) {
  return useApiInfiniteQuery<any>({
    url: `/posts/${postId}/comments`,
    pageSize,
    queryKey: ['comments', 'infinite', postId],
    enabled: !!postId,
  })
}

// 通知列表无限查询
export function useInfiniteNotifications(pageSize: number = 20) {
  return useApiInfiniteQuery<any>({
    url: '/notifications',
    pageSize,
    queryKey: ['notifications', 'infinite'],
  })
}

// 搜索结果无限查询
export function useInfiniteSearch<T = any>(
  endpoint: string,
  searchTerm: string,
  pageSize: number = 20
) {
  return useApiInfiniteQuery<T>({
    url: endpoint,
    params: { search: searchTerm },
    pageSize,
    queryKey: [endpoint, 'search', 'infinite', searchTerm],
    enabled: searchTerm.length > 0,
  })
}

// 时间线无限查询（按时间倒序）
export function useInfiniteTimeline<T = any>(
  endpoint: string,
  pageSize: number = 20
) {
  return useApiInfiniteQuery<T>({
    url: endpoint,
    pageSize,
    queryKey: [endpoint, 'timeline', 'infinite'],
    // 自定义分页参数，使用时间戳
    getNextPageParam: (lastPage) => {
      if (!lastPage.data.hasNext || lastPage.data.items.length === 0) {
        return undefined
      }
      // 假设数据按时间倒序排列，使用最后一项的时间戳作为下一页参数
      const lastItem = lastPage.data.items[lastPage.data.items.length - 1] as any
      return lastItem.createdAt || lastItem.timestamp
    },
    pageParamName: 'before', // 使用 before 参数而不是 page
  })
}

// 分类数据无限查询
export function useInfiniteByCategory<T = any>(
  endpoint: string,
  category: string,
  pageSize: number = 20
) {
  return useApiInfiniteQuery<T>({
    url: endpoint,
    params: { category },
    pageSize,
    queryKey: [endpoint, 'category', 'infinite', category],
    enabled: !!category,
  })
}

// 用户相关数据无限查询
export function useInfiniteUserData<T = any>(
  endpoint: string,
  userId: string | number,
  pageSize: number = 20
) {
  return useApiInfiniteQuery<T>({
    url: endpoint,
    params: { userId },
    pageSize,
    queryKey: [endpoint, 'user', 'infinite', userId],
    enabled: !!userId,
  })
}

// 无限查询工具函数

// 获取所有页面的数据
export function getAllPagesData<T = any>(
  infiniteQueryResult: UseInfiniteQueryResult<PaginatedResponse<T>>
): T[] {
  if (!infiniteQueryResult.data) return []
  
  return infiniteQueryResult.data.pages.reduce<T[]>((acc, page) => {
    return acc.concat(page.data.items)
  }, [])
}

// 获取总数据量
export function getTotalCount<T = any>(
  infiniteQueryResult: UseInfiniteQueryResult<PaginatedResponse<T>>
): number {
  if (!infiniteQueryResult.data || infiniteQueryResult.data.pages.length === 0) {
    return 0
  }
  
  // 返回第一页的总数
  return infiniteQueryResult.data.pages[0].data.total
}

// 检查是否有更多数据
export function hasMoreData<T = any>(
  infiniteQueryResult: UseInfiniteQueryResult<PaginatedResponse<T>>
): boolean {
  return infiniteQueryResult.hasNextPage || false
}

// 检查是否正在加载更多
export function isLoadingMore<T = any>(
  infiniteQueryResult: UseInfiniteQueryResult<PaginatedResponse<T>>
): boolean {
  return infiniteQueryResult.isFetchingNextPage || false
}

// 获取当前已加载的页数
export function getLoadedPagesCount<T = any>(
  infiniteQueryResult: UseInfiniteQueryResult<PaginatedResponse<T>>
): number {
  return infiniteQueryResult.data?.pages.length || 0
}

// 重置无限查询到第一页
export function resetToFirstPage<T = any>(
  infiniteQueryResult: UseInfiniteQueryResult<PaginatedResponse<T>>
): void {
  infiniteQueryResult.remove()
  infiniteQueryResult.refetch()
}

// 自定义 Hook：无限滚动加载
export function useInfiniteScroll<T = any>(
  infiniteQueryResult: UseInfiniteQueryResult<PaginatedResponse<T>>,
  threshold: number = 100
) {
  const { fetchNextPage, hasNextPage, isFetchingNextPage } = infiniteQueryResult

  const handleScroll = () => {
    if (
      hasNextPage &&
      !isFetchingNextPage &&
      window.innerHeight + window.scrollY >= document.body.offsetHeight - threshold
    ) {
      fetchNextPage()
    }
  }

  // 在组件中使用 useEffect 监听滚动事件
  const attachScrollListener = () => {
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }

  return {
    attachScrollListener,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
  }
}
