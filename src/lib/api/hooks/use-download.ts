/**
 * 文件下载 Hook
 * 提供文件下载功能和进度管理
 */

import { useState, useCallback } from 'react'
import { useMutation } from '@tanstack/react-query'
import { toast } from 'sonner'
import { api } from '../client'
import type { DownloadConfig } from '../types'
import { createDownloadLink, formatFileSize } from '../utils'

// 下载状态
export interface DownloadState {
  progress: number
  isDownloading: boolean
  error: string | null
  downloadedSize: number
  totalSize: number
}

// 下载 Hook 选项
export interface UseDownloadOptions extends DownloadConfig {
  // 下载前的确认
  beforeDownload?: (url: string, filename?: string) => boolean | Promise<boolean>
  
  // 下载成功回调
  onDownloadSuccess?: (blob: Blob, filename?: string) => void
  
  // 下载失败回调
  onDownloadError?: (error: any) => void
}

// 文件下载 Hook
export function useDownload(options: UseDownloadOptions = {}) {
  const {
    beforeDownload,
    onDownloadSuccess,
    onDownloadError,
    showLoading = true,
    showError = true,
    showSuccess = true,
    ...config
  } = options

  // 下载状态
  const [downloadState, setDownloadState] = useState<DownloadState>({
    progress: 0,
    isDownloading: false,
    error: null,
    downloadedSize: 0,
    totalSize: 0,
  })

  // 下载 Mutation
  const downloadMutation = useMutation<Blob, Error, { url: string; filename?: string }>({
    mutationFn: async ({ url, filename }) => {
      // 下载前确认
      if (beforeDownload) {
        const canDownload = await beforeDownload(url, filename)
        if (!canDownload) {
          throw new Error('下载被取消')
        }
      }

      // 执行下载
      return api.download(url, {
        ...config,
        filename,
        showLoading,
        showError,
        showSuccess,
        onProgress: (progress) => {
          setDownloadState(prev => ({ ...prev, progress }))
          options.onProgress?.(progress)
        },
      })
    },
    onMutate: () => {
      setDownloadState(prev => ({
        ...prev,
        isDownloading: true,
        error: null,
        progress: 0,
        downloadedSize: 0,
        totalSize: 0,
      }))
    },
    onSuccess: (blob, { filename }) => {
      setDownloadState(prev => ({
        ...prev,
        progress: 100,
        downloadedSize: blob.size,
        totalSize: blob.size,
      }))

      onDownloadSuccess?.(blob, filename)

      if (showSuccess) {
        toast.success(`文件下载完成${filename ? `: ${filename}` : ''}`)
      }
    },
    onError: (error) => {
      setDownloadState(prev => ({
        ...prev,
        error: error.message,
        progress: 0,
      }))

      onDownloadError?.(error)

      if (showError) {
        toast.error(`下载失败: ${error.message}`)
      }
    },
    onSettled: () => {
      setDownloadState(prev => ({ ...prev, isDownloading: false }))
    },
  })

  // 下载文件
  const downloadFile = useCallback((url: string, filename?: string) => {
    downloadMutation.mutate({ url, filename })
  }, [downloadMutation])

  // 批量下载
  const downloadFiles = useCallback(async (
    files: Array<{ url: string; filename?: string }>
  ) => {
    setDownloadState(prev => ({ ...prev, isDownloading: true }))

    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i]
        await downloadMutation.mutateAsync(file)
        
        // 更新总体进度
        const overallProgress = ((i + 1) / files.length) * 100
        setDownloadState(prev => ({ ...prev, progress: overallProgress }))
      }

      if (showSuccess) {
        toast.success(`成功下载 ${files.length} 个文件`)
      }
    } catch (error) {
      if (showError) {
        toast.error(`批量下载失败: ${(error as Error).message}`)
      }
    } finally {
      setDownloadState(prev => ({ ...prev, isDownloading: false }))
    }
  }, [downloadMutation, showSuccess, showError])

  // 重置状态
  const reset = useCallback(() => {
    setDownloadState({
      progress: 0,
      isDownloading: false,
      error: null,
      downloadedSize: 0,
      totalSize: 0,
    })
    downloadMutation.reset()
  }, [downloadMutation])

  return {
    // 状态
    ...downloadState,
    
    // 方法
    downloadFile,
    downloadFiles,
    reset,
    
    // Mutation 对象
    downloadMutation,
  }
}

// 图片下载 Hook（带预览）
export function useImageDownload(options: UseDownloadOptions = {}) {
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)

  const download = useDownload({
    ...options,
    onDownloadSuccess: (blob, filename) => {
      // 生成预览 URL
      const url = URL.createObjectURL(blob)
      setPreviewUrl(url)

      options.onDownloadSuccess?.(blob, filename)
    },
  })

  const clearPreview = useCallback(() => {
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl)
      setPreviewUrl(null)
    }
  }, [previewUrl])

  return {
    ...download,
    previewUrl,
    clearPreview,
  }
}

// 流式下载 Hook（大文件）
export function useStreamDownload(options: UseDownloadOptions = {}) {
  const [chunks, setChunks] = useState<Blob[]>([])

  const streamDownloadMutation = useMutation<Blob, Error, { url: string; filename?: string }>({
    mutationFn: async ({ url, filename }) => {
      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
      })

      if (!response.ok) {
        throw new Error(`下载失败: ${response.statusText}`)
      }

      const contentLength = response.headers.get('content-length')
      const total = contentLength ? parseInt(contentLength, 10) : 0

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('无法读取响应流')
      }

      const chunks: Uint8Array[] = []
      let receivedLength = 0

      while (true) {
        const { done, value } = await reader.read()

        if (done) break

        chunks.push(value)
        receivedLength += value.length

        // 更新进度
        if (total > 0) {
          const progress = (receivedLength / total) * 100
          setDownloadState(prev => ({
            ...prev,
            progress,
            downloadedSize: receivedLength,
            totalSize: total,
          }))
          options.onProgress?.(progress)
        }
      }

      // 合并所有块
      const allChunks = new Uint8Array(receivedLength)
      let position = 0
      for (const chunk of chunks) {
        allChunks.set(chunk, position)
        position += chunk.length
      }

      const blob = new Blob([allChunks])
      
      // 自动下载
      if (filename) {
        createDownloadLink(blob, filename)
      }

      return blob
    },
  })

  const [downloadState, setDownloadState] = useState<DownloadState>({
    progress: 0,
    isDownloading: false,
    error: null,
    downloadedSize: 0,
    totalSize: 0,
  })

  return {
    ...streamDownloadMutation,
    ...downloadState,
    chunks,
    downloadFile: streamDownloadMutation.mutate,
  }
}

// 断点续传下载 Hook
export function useResumableDownload(options: UseDownloadOptions = {}) {
  const [resumeData, setResumeData] = useState<{
    url: string
    filename: string
    downloadedSize: number
    totalSize: number
  } | null>(null)

  const resumableDownloadMutation = useMutation<Blob, Error, { 
    url: string 
    filename?: string 
    resume?: boolean 
  }>({
    mutationFn: async ({ url, filename, resume = false }) => {
      let startByte = 0
      
      if (resume && resumeData && resumeData.url === url) {
        startByte = resumeData.downloadedSize
      }

      const headers: Record<string, string> = {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
      }

      if (startByte > 0) {
        headers['Range'] = `bytes=${startByte}-`
      }

      const response = await fetch(url, { headers })

      if (!response.ok && response.status !== 206) {
        throw new Error(`下载失败: ${response.statusText}`)
      }

      const contentLength = response.headers.get('content-length')
      const contentRange = response.headers.get('content-range')
      
      let total = 0
      if (contentRange) {
        const match = contentRange.match(/bytes \d+-\d+\/(\d+)/)
        total = match ? parseInt(match[1], 10) : 0
      } else if (contentLength) {
        total = parseInt(contentLength, 10)
      }

      // 保存断点信息
      if (filename) {
        setResumeData({
          url,
          filename,
          downloadedSize: startByte,
          totalSize: total,
        })
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('无法读取响应流')
      }

      const chunks: Uint8Array[] = []
      let receivedLength = startByte

      while (true) {
        const { done, value } = await reader.read()

        if (done) break

        chunks.push(value)
        receivedLength += value.length

        // 更新断点信息
        if (resumeData) {
          setResumeData(prev => prev ? {
            ...prev,
            downloadedSize: receivedLength,
          } : null)
        }

        // 更新进度
        if (total > 0) {
          const progress = (receivedLength / total) * 100
          options.onProgress?.(progress)
        }
      }

      // 合并所有块
      const allChunks = new Uint8Array(receivedLength - startByte)
      let position = 0
      for (const chunk of chunks) {
        allChunks.set(chunk, position)
        position += chunk.length
      }

      const blob = new Blob([allChunks])
      
      // 清除断点信息
      setResumeData(null)

      return blob
    },
  })

  const resumeDownload = useCallback(() => {
    if (resumeData) {
      resumableDownloadMutation.mutate({
        url: resumeData.url,
        filename: resumeData.filename,
        resume: true,
      })
    }
  }, [resumeData, resumableDownloadMutation])

  const cancelDownload = useCallback(() => {
    setResumeData(null)
    resumableDownloadMutation.reset()
  }, [resumableDownloadMutation])

  return {
    ...resumableDownloadMutation,
    resumeData,
    resumeDownload,
    cancelDownload,
    downloadFile: resumableDownloadMutation.mutate,
  }
}
