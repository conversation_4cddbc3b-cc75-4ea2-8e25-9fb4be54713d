/**
 * 请求/响应拦截器
 * 处理认证、loading、错误等通用逻辑
 */

import type { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios'
import { toast } from 'sonner'
import { useAuthStore } from '@/stores/authStore'
import type { ApiResponse, ApiRequestConfig, AuthTokens } from './types'
import { REQUEST_HEADERS, STORAGE_KEYS, API_ENDPOINTS } from './config'
import {
  generateRequestId,
  generateRequestSignature,
  sanitizeInput,
  encryptData,
  StatsManager
} from './utils'
import { handleApiError } from './error-handler'

// Loading 状态管理
class LoadingManager {
  private static loadingCount = 0
  private static loadingToastId: string | number | null = null

  static show(message: string = '加载中...'): void {
    this.loadingCount++

    if (this.loadingCount === 1) {
      this.loadingToastId = toast.loading(message, {
        duration: Infinity,
      })
    }
  }

  static hide(): void {
    this.loadingCount = Math.max(0, this.loadingCount - 1)

    if (this.loadingCount === 0 && this.loadingToastId) {
      toast.dismiss(this.loadingToastId)
      this.loadingToastId = null
    }
  }

  static forceHide(): void {
    this.loadingCount = 0
    if (this.loadingToastId) {
      toast.dismiss(this.loadingToastId)
      this.loadingToastId = null
    }
  }
}

// 请求拦截器
export const createRequestInterceptor = () => {
  return {
    onFulfilled: async (config: AxiosRequestConfig & ApiRequestConfig) => {
      const startTime = Date.now()
      config.metadata = { startTime }

      // 生成请求 ID
      const requestId = generateRequestId()
      config.headers = config.headers || {}
      config.headers[REQUEST_HEADERS.X_REQUEST_ID] = requestId

      // 添加客户端版本信息
      config.headers[REQUEST_HEADERS.X_CLIENT_VERSION] = import.meta.env.VITE_APP_VERSION || '1.0.0'

      // 获取认证信息
      const authStore = useAuthStore.getState()
      const { accessToken, refreshToken } = authStore

      // 添加认证头
      if (config.requireAuth !== false && accessToken) {
        config.headers[REQUEST_HEADERS.AUTHORIZATION] = `Bearer ${accessToken}`
      }

      // 添加 CSRF Token
      const csrfToken = localStorage.getItem(STORAGE_KEYS.CSRF_TOKEN)
      if (csrfToken && ['POST', 'PUT', 'DELETE', 'PATCH'].includes(config.method?.toUpperCase() || '')) {
        config.headers[REQUEST_HEADERS.X_CSRF_TOKEN] = csrfToken
      }

      // XSS 防护 - 清理请求数据
      if (config.data) {
        config.data = sanitizeInput(config.data)
      }

      // 数据加密
      if (config.encrypt && config.data) {
        const encryptionKey = import.meta.env.VITE_ENCRYPTION_KEY
        if (encryptionKey) {
          config.data = {
            encrypted: true,
            data: encryptData(config.data, encryptionKey)
          }
        }
      }

      // 请求签名
      if (config.requestSigning) {
        const signature = generateRequestSignature(
          config.method || 'GET',
          config.url || '',
          config.data,
          Date.now()
        )
        config.headers['X-Signature'] = signature
      }

      // 显示 loading
      if (config.showLoading !== false) {
        LoadingManager.show(config.loadingMessage || '加载中...')
      }

      // 更新统计
      StatsManager.increment('total')
      StatsManager.increment('pending')

      // 开发环境日志
      if (import.meta.env.DEV) {
        console.group(`📤 API Request: ${config.method?.toUpperCase()} ${config.url}`)
        console.log('Config:', config)
        console.log('Headers:', config.headers)
        console.log('Data:', config.data)
        console.groupEnd()
      }

      return config
    },

    onRejected: (error: AxiosError) => {
      LoadingManager.hide()
      StatsManager.decrement('pending')
      StatsManager.increment('error')

      return Promise.reject(error)
    }
  }
}

// 响应拦截器
export const createResponseInterceptor = () => {
  return {
    onFulfilled: async (response: AxiosResponse<ApiResponse>) => {
      const config = response.config as AxiosRequestConfig & ApiRequestConfig
      const startTime = config.metadata?.startTime || Date.now()
      const duration = Date.now() - startTime

      // 隐藏 loading
      if (config.showLoading !== false) {
        LoadingManager.hide()
      }

      // 更新统计
      StatsManager.decrement('pending')
      StatsManager.increment('success')
      StatsManager.updateAverageTime(duration)

      // 开发环境日志
      if (import.meta.env.DEV) {
        console.group(`📥 API Response: ${config.method?.toUpperCase()} ${config.url} (${duration}ms)`)
        console.log('Status:', response.status)
        console.log('Headers:', response.headers)
        console.log('Data:', response.data)
        console.groupEnd()
      }

      // 检查业务逻辑错误
      if (response.data && !response.data.success) {
        const businessError = {
          name: 'BusinessError',
          message: response.data.message || '业务逻辑错误',
          code: response.data.code || 'BUSINESS_ERROR',
          details: response.data,
          requestId: response.headers[REQUEST_HEADERS.X_REQUEST_ID.toLowerCase()],
        }

        throw businessError
      }

      // 显示成功提示
      if (config.showSuccess && response.data.message) {
        toast.success(response.data.message)
      }

      // 返回标准化数据
      return response
    },

    onRejected: async (error: AxiosError) => {
      const config = error.config as AxiosRequestConfig & ApiRequestConfig
      const startTime = config?.metadata?.startTime || Date.now()
      const duration = Date.now() - startTime

      // 隐藏 loading
      if (config?.showLoading !== false) {
        LoadingManager.hide()
      }

      // 更新统计
      StatsManager.decrement('pending')
      StatsManager.increment('error')
      StatsManager.updateAverageTime(duration)

      // 处理 401 未授权错误 - 自动刷新 token
      if (error.response?.status === 401 && config?.requireAuth !== false) {
        const authStore = useAuthStore.getState()
        const { refreshToken, logout } = authStore

        if (refreshToken && !config?.url?.includes(API_ENDPOINTS.AUTH.REFRESH)) {
          try {
            // 尝试刷新 token
            const refreshed = await refreshAccessToken(refreshToken)
            if (refreshed) {
              // 重新发送原请求
              const newConfig = {
                ...config,
                headers: {
                  ...config.headers,
                  [REQUEST_HEADERS.AUTHORIZATION]: `Bearer ${refreshed.accessToken}`,
                },
              }

              // 使用 axios 实例重新发送请求
              const axios = (await import('./client')).apiClient
              return axios.request(newConfig)
            }
          } catch (refreshError) {
            console.error('Token refresh failed:', refreshError)
          }
        }

        // 刷新失败，清除认证信息并跳转登录
        logout()

        // 延迟跳转，避免在请求过程中立即跳转
        setTimeout(() => {
          if (window.location.pathname !== '/sign-in') {
            window.location.href = '/sign-in'
          }
        }, 1000)
      }

      // 处理其他错误
      const processedError = handleApiError(error, config)

      return Promise.reject(processedError)
    }
  }
}

// 刷新访问令牌
async function refreshAccessToken(refreshToken: string): Promise<AuthTokens | null> {
  try {
    const axios = (await import('./client')).apiClient

    const response = await axios.post<ApiResponse<AuthTokens>>(
      API_ENDPOINTS.AUTH.REFRESH,
      { refreshToken },
      {
        requireAuth: false,
        showLoading: false,
        showError: false,
      }
    )

    if (response.data.success && response.data.data) {
      const tokens = response.data.data
      const authStore = useAuthStore.getState()

      // 更新存储的 token
      authStore.setAccessToken(tokens.accessToken)

      return tokens
    }
  } catch (error) {
    console.error('Failed to refresh token:', error)
  }

  return null
}

// 请求重试拦截器
export const createRetryInterceptor = (maxRetries: number = 3, retryDelay: number = 1000) => {
  return {
    onRejected: async (error: AxiosError) => {
      const config = error.config as any

      // 初始化重试计数
      if (!config.__retryCount) {
        config.__retryCount = 0
      }

      // 检查是否应该重试
      const shouldRetry = config.__retryCount < maxRetries &&
                         (error.code === 'ECONNABORTED' ||
                          error.code === 'NETWORK_ERROR' ||
                          (error.response && error.response.status >= 500))

      if (shouldRetry) {
        config.__retryCount++

        // 计算延迟时间（指数退避）
        const delay = retryDelay * Math.pow(2, config.__retryCount - 1)

        console.log(`Retrying request (${config.__retryCount}/${maxRetries}) after ${delay}ms`)

        // 延迟后重试
        await new Promise(resolve => setTimeout(resolve, delay))

        const axios = (await import('./client')).apiClient
        return axios.request(config)
      }

      return Promise.reject(error)
    }
  }
}

// 请求去重拦截器
export const createDedupeInterceptor = () => {
  const pendingRequests = new Map<string, Promise<any>>()

  return {
    onFulfilled: (config: AxiosRequestConfig & ApiRequestConfig) => {
      if (config.dedupe !== false) {
        const key = `${config.method}:${config.url}:${JSON.stringify(config.data || {})}`

        if (pendingRequests.has(key)) {
          // 返回已存在的请求 Promise
          throw {
            __DEDUPE__: true,
            promise: pendingRequests.get(key)
          }
        }

        // 创建新的请求 Promise
        const requestPromise = new Promise((resolve, reject) => {
          config.__dedupeResolve = resolve
          config.__dedupeReject = reject
        })

        pendingRequests.set(key, requestPromise)

        // 请求完成后清理
        requestPromise.finally(() => {
          pendingRequests.delete(key)
        })
      }

      return config
    },

    onRejected: (error: any) => {
      if (error.__DEDUPE__) {
        return error.promise
      }
      return Promise.reject(error)
    }
  }
}

// 导出 Loading 管理器
export { LoadingManager }
