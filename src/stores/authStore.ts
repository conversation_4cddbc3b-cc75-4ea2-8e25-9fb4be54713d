import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import AuthStorage, { type AuthUser } from '@/utils/auth-storage'

interface AuthState {
  user: AuthUser | null
  accessToken: string
  refreshToken: string
  isAuthenticated: boolean
  isLoading: boolean

  // Actions
  setUser: (user: AuthUser | null) => void
  setAccessToken: (accessToken: string) => void
  setRefreshToken: (refreshToken: string) => void
  setLoading: (loading: boolean) => void
  login: (user: AuthUser, token: string, refreshToken?: string) => void
  logout: () => void
  reset: () => void

  // Computed
  getUserDisplayName: () => string
  getUserInitials: () => string

  // Storage utilities
  updateLastActivity: () => void
  checkTokenExpiry: () => boolean
  shouldRefreshToken: () => boolean
}

// 初始化状态
const getInitialState = () => {
  const authInfo = AuthStorage.getAuthInfo()

  return {
    user: authInfo.user || null,
    accessToken: authInfo.accessToken || '',
    refreshToken: authInfo.refreshToken || '',
    isAuthenticated: authInfo.isValid,
    isLoading: false,
  }
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      ...getInitialState(),

      setUser: (user) => {
        set({ user })
        if (user) {
          AuthStorage.setUserData(user)
        } else {
          AuthStorage.removeUserData()
        }
      },

      setAccessToken: (accessToken) => {
        set({ accessToken, isAuthenticated: !!accessToken })
        if (accessToken) {
          AuthStorage.setAccessToken(accessToken)
        } else {
          AuthStorage.removeAccessToken()
        }
      },

      setRefreshToken: (refreshToken) => {
        set({ refreshToken })
        if (refreshToken) {
          AuthStorage.setRefreshToken(refreshToken)
        } else {
          AuthStorage.removeRefreshToken()
        }
      },

      setLoading: (isLoading) => set({ isLoading }),

      login: (user, token, refreshToken) => {
        set({
          user,
          accessToken: token,
          refreshToken: refreshToken || '',
          isAuthenticated: true,
          isLoading: false
        })
        AuthStorage.setLoginInfo(user, token, refreshToken)
      },

      logout: () => {
        set({
          user: null,
          accessToken: '',
          refreshToken: '',
          isAuthenticated: false,
          isLoading: false
        })
        AuthStorage.clearAll()
      },

      reset: () => {
        set({
          user: null,
          accessToken: '',
          refreshToken: '',
          isAuthenticated: false,
          isLoading: false
        })
        AuthStorage.clearAll()
      },

      getUserDisplayName: () => {
        return AuthStorage.getUserDisplayName()
      },

      getUserInitials: () => {
        return AuthStorage.getUserInitials()
      },

      updateLastActivity: () => {
        AuthStorage.updateLastActivity()
      },

      checkTokenExpiry: () => {
        const remainingTime = AuthStorage.getTokenRemainingTime()
        if (remainingTime <= 0) {
          // Token 已过期，执行登出
          get().logout()
          return false
        }
        return true
      },

      shouldRefreshToken: () => {
        return AuthStorage.shouldRefreshToken()
      },
    }),
    {
      name: 'auth-storage-zustand',
      // 只持久化基本状态，实际数据由 AuthStorage 管理
      partialize: (_state) => ({
        // 不持久化任何数据，因为 AuthStorage 已经处理了持久化
        // 这里只是为了保持 Zustand 的结构
        _placeholder: true,
      }),
      // 跳过 Zustand 的持久化，完全依赖 AuthStorage
      skipHydration: true,
    }
  )
)

// export const useAuth = () => useAuthStore((state) => state.auth)
