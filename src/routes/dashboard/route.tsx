import { createFileRoute, redirect } from '@tanstack/react-router'
import { AuthenticatedLayout } from '@/components/layout/authenticated-layout'
import { useAuthStore } from '@/stores/authStore'

export const Route = createFileRoute('/dashboard')({
  beforeLoad: ({ location }) => {
    const { isAuthenticated, accessToken } = useAuthStore.getState()

    // 检查认证状态
    if (!isAuthenticated || !accessToken) {
      throw redirect({
        to: '/sign-in',
        search: {
          redirect: location.href,
        },
      })
    }
  },
  component: AuthenticatedLayout,
})
