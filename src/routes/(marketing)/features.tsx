import { createFileRoute } from '@tanstack/react-router'
import { useTranslation } from 'react-i18next'
import {
  IconPalette,
  IconDeviceMobile,
  IconBolt,
  IconShield,
  IconCode,
  IconPuzzle,
  IconChart,
  IconUsers,
  IconGlobe,
  IconMoon,
  IconBrandReact,
  IconBrandTypescript
} from '@tabler/icons-react'
import { MarketingLayout } from '@/components/layout/marketing-layout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

// 主要功能特性
const mainFeatures = [
  {
    icon: IconPalette,
    title: '现代化设计系统',
    description: '基于 Shadcn UI 的完整设计系统，提供一致的视觉体验和交互模式。',
    highlights: ['统一的颜色系统', '可定制主题', '暗色模式支持', '响应式设计']
  },
  {
    icon: IconBolt,
    title: '高性能架构',
    description: '采用最新的 React 技术栈，确保应用的高性能和可扩展性。',
    highlights: ['React 19', 'TypeScript', 'Vite 构建', 'Tree Shaking']
  },
  {
    icon: IconPuzzle,
    title: '丰富的组件库',
    description: '提供 50+ 精心设计的组件，覆盖管理后台的各种使用场景。',
    highlights: ['表单组件', '数据表格', '图表组件', '导航组件']
  },
  {
    icon: IconCode,
    title: '开发者友好',
    description: '完整的 TypeScript 支持，优秀的开发体验和详细的文档。',
    highlights: ['类型安全', '代码提示', '热重载', '详细文档']
  }
]

// 技术特性
const techFeatures = [
  {
    icon: IconBrandReact,
    title: 'React 19',
    description: '使用最新的 React 版本，享受最新的特性和性能优化。'
  },
  {
    icon: IconBrandTypescript,
    title: 'TypeScript',
    description: '完整的 TypeScript 支持，提供类型安全和更好的开发体验。'
  },
  {
    icon: IconDeviceMobile,
    title: '响应式设计',
    description: '完美适配各种设备尺寸，从手机到桌面端都有优秀的体验。'
  },
  {
    icon: IconMoon,
    title: '暗色模式',
    description: '内置暗色模式支持，用户可以根据偏好自由切换。'
  },
  {
    icon: IconGlobe,
    title: '国际化',
    description: '内置 i18n 支持，轻松实现多语言应用。'
  },
  {
    icon: IconShield,
    title: '安全可靠',
    description: '遵循最佳安全实践，保护用户数据和应用安全。'
  }
]

// 业务功能
const businessFeatures = [
  {
    icon: IconChart,
    title: '数据可视化',
    description: '强大的图表组件，支持各种数据可视化需求。',
    image: '/images/charts-preview.jpg'
  },
  {
    icon: IconUsers,
    title: '用户管理',
    description: '完整的用户管理系统，支持角色权限控制。',
    image: '/images/users-preview.jpg'
  },
  {
    icon: IconPuzzle,
    title: '任务管理',
    description: '灵活的任务管理功能，提高团队协作效率。',
    image: '/images/tasks-preview.jpg'
  }
]

function FeaturesPage() {
  const { t } = useTranslation()

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="container py-24">
        <div className="mx-auto max-w-4xl text-center">
          <Badge variant="outline" className="mb-4">
            ✨ 功能特性
          </Badge>
          <h1 className="text-4xl font-bold tracking-tight sm:text-6xl mb-6">
            强大的功能，优雅的设计
          </h1>
          <p className="text-xl text-muted-foreground mb-8">
            Shadcn Admin 提供了构建现代管理后台所需的一切功能，让您专注于业务逻辑而不是基础设施。
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" asChild>
              <a href="/pricing">开始使用</a>
            </Button>
            <Button variant="outline" size="lg" asChild>
              <a href="/demo">查看演示</a>
            </Button>
          </div>
        </div>
      </section>

      {/* Main Features */}
      <section className="container py-16">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4">核心功能</h2>
            <p className="text-xl text-muted-foreground">
              为现代 Web 应用设计的完整解决方案
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {mainFeatures.map((feature, index) => (
              <Card key={index} className="relative overflow-hidden">
                <CardHeader>
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                    <feature.icon className="h-6 w-6 text-primary" />
                  </div>
                  <CardTitle>{feature.title}</CardTitle>
                  <CardDescription>{feature.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {feature.highlights.map((highlight, idx) => (
                      <Badge key={idx} variant="secondary" className="text-xs">
                        {highlight}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Tech Features */}
      <section className="bg-muted/50 py-16">
        <div className="container">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold mb-4">技术优势</h2>
              <p className="text-xl text-muted-foreground">
                基于最新技术栈，确保应用的现代化和可维护性
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {techFeatures.map((feature, index) => (
                <Card key={index} className="text-center">
                  <CardHeader>
                    <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                      <feature.icon className="h-8 w-8 text-primary" />
                    </div>
                    <CardTitle>{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">{feature.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Business Features */}
      <section className="container py-16">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4">业务功能</h2>
            <p className="text-xl text-muted-foreground">
              开箱即用的业务功能模块，快速构建完整应用
            </p>
          </div>

          <div className="space-y-16">
            {businessFeatures.map((feature, index) => (
              <div key={index} className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-center ${
                index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''
              }`}>
                <div className={index % 2 === 1 ? 'lg:col-start-2' : ''}>
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-6">
                    <feature.icon className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="text-2xl font-bold mb-4">{feature.title}</h3>
                  <p className="text-lg text-muted-foreground mb-6">{feature.description}</p>
                  <Button asChild>
                    <a href="/demo">查看演示</a>
                  </Button>
                </div>
                <div className={`${index % 2 === 1 ? 'lg:col-start-1' : ''}`}>
                  <div className="aspect-video bg-gradient-to-br from-primary/20 to-secondary/20 rounded-lg flex items-center justify-center">
                    <span className="text-muted-foreground">功能预览图</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="bg-muted/50 py-16">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
              <div>
                <div className="text-3xl font-bold text-primary mb-2">50+</div>
                <div className="text-muted-foreground">组件</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-primary mb-2">15K+</div>
                <div className="text-muted-foreground">GitHub Stars</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-primary mb-2">1000+</div>
                <div className="text-muted-foreground">项目使用</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-primary mb-2">99.9%</div>
                <div className="text-muted-foreground">可用性</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container py-16">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold mb-4">准备开始了吗？</h2>
          <p className="text-xl text-muted-foreground mb-8">
            立即体验 Shadcn Admin 的强大功能，构建您的下一个项目。
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" asChild>
              <a href="/pricing">选择方案</a>
            </Button>
            <Button variant="outline" size="lg" asChild>
              <a href="/contact">联系我们</a>
            </Button>
          </div>
        </div>
      </section>
    </div>
  )
}

export const Route = createFileRoute('/(marketing)/features')({
  component: () => (
    <MarketingLayout>
      <FeaturesPage />
    </MarketingLayout>
  ),
})
