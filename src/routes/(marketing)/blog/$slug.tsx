import { create<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from '@tanstack/react-router'
import { useTranslation } from 'react-i18next'
import { IconArrowLeft, IconCalendar, IconUser, IconClock, IconShare } from '@tabler/icons-react'
import { MarketingLayout } from '@/components/layout/marketing-layout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { toast } from 'sonner'
import { getBlogPostBySlug, getRelatedPosts } from '@/data/blog-posts'

function BlogPostPage() {
  const { t } = useTranslation()
  const { slug } = Route.useParams()

  // 获取博客文章数据
  const blogPost = getBlogPostBySlug(slug)
  const relatedPosts = blogPost ? getRelatedPosts(blogPost.id) : []

  // 如果文章不存在，显示404
  if (!blogPost) {
    return (
      <div className="container py-24 text-center">
        <h1 className="text-2xl font-bold mb-4">文章未找到</h1>
        <p className="text-muted-foreground mb-8">您访问的文章不存在或已被删除。</p>
        <Button asChild>
          <Link to="/blog">返回博客</Link>
        </Button>
      </div>
    )
  }



  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: blogPost.title,
        text: blogPost.excerpt,
        url: window.location.href,
      })
    } else {
      // 复制链接到剪贴板
      navigator.clipboard.writeText(window.location.href)
      toast.success('链接已复制到剪贴板')
    }
  }

  return (
    <div className="container py-8 sm:py-12">
      {/* 返回按钮 */}
      <div className="mb-6 sm:mb-8">
        <Button variant="ghost" asChild className="text-sm sm:text-base">
          <Link to="/blog">
            <IconArrowLeft className="h-4 w-4 mr-2" />
            {t('blog.backToBlog')}
          </Link>
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 lg:gap-12">
        {/* 主要内容 */}
        <div className="lg:col-span-3">
          <article>
            {/* 文章头部 */}
            <header className="mb-8">
              <div className="flex items-center gap-2 mb-4">
                <Badge variant="secondary">{blogPost.category}</Badge>
                {blogPost.featured && <Badge variant="outline">特色</Badge>}
              </div>

              <h1 className="text-4xl font-bold tracking-tight mb-4">
                {blogPost.title}
              </h1>

              <p className="text-xl text-muted-foreground mb-6">
                {blogPost.excerpt}
              </p>

              {/* 作者信息区域 - 优化移动端显示 */}
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                {/* 作者信息 */}
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                    <IconUser className="h-5 w-5 sm:h-6 sm:w-6" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <div className="font-medium text-foreground text-sm sm:text-base">{blogPost.author.name}</div>
                    <div className="text-xs sm:text-sm text-muted-foreground truncate">{blogPost.author.bio}</div>
                  </div>
                </div>

                {/* 分享按钮 */}
                <Button variant="outline" size="sm" onClick={handleShare} className="self-start sm:self-auto">
                  <IconShare className="h-4 w-4 mr-2" />
                  <span className="hidden sm:inline">{t('blog.shareArticle')}</span>
                  <span className="sm:hidden">分享</span>
                </Button>
              </div>

              {/* 文章元信息 - 移动端优化布局 */}
              <div className="flex flex-wrap items-center gap-3 sm:gap-6 text-xs sm:text-sm text-muted-foreground mt-4 pt-4 border-t">
                <div className="flex items-center gap-1.5">
                  <IconCalendar className="h-3.5 w-3.5 sm:h-4 sm:w-4 flex-shrink-0" />
                  <span className="whitespace-nowrap">{blogPost.publishedAt}</span>
                </div>
                <div className="flex items-center gap-1.5">
                  <IconClock className="h-3.5 w-3.5 sm:h-4 sm:w-4 flex-shrink-0" />
                  <span className="whitespace-nowrap">{blogPost.readTime} {t('blog.readTime')}</span>
                </div>
              </div>
            </header>

            {/* 文章内容 */}
            <div className="prose prose-sm sm:prose-lg max-w-none mb-8 sm:mb-12">
              <div className="whitespace-pre-wrap text-sm sm:text-base leading-relaxed">{blogPost.content}</div>
            </div>

            {/* 标签 */}
            <div className="mb-8 sm:mb-12">
              <h3 className="text-sm sm:text-base font-medium mb-3 sm:mb-4">{t('blog.tags')}</h3>
              <div className="flex flex-wrap gap-2">
                {blogPost.tags.map((tag: string) => (
                  <Badge key={tag} variant="outline" className="text-xs sm:text-sm">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>


          </article>
        </div>

        {/* 侧边栏 - 移动端优化 */}
        <div className="lg:col-span-1">
          <div className="lg:sticky lg:top-8">
            {/* 相关文章 */}
            {relatedPosts.length > 0 && (
              <Card>
                <CardHeader className="pb-4">
                  <CardTitle className="text-lg sm:text-xl">{t('blog.relatedPosts')}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4 sm:space-y-6">
                  {relatedPosts.map((post) => (
                    <div key={post.id} className="pb-4 border-b border-border last:border-b-0 last:pb-0">
                      <h4 className="font-medium mb-2 text-sm sm:text-base leading-snug">
                        <a
                          href={`/blog/${post.slug}`}
                          className="hover:text-primary transition-colors line-clamp-2"
                        >
                          {post.title}
                        </a>
                      </h4>
                      <p className="text-xs sm:text-sm text-muted-foreground mb-3 line-clamp-2 leading-relaxed">
                        {post.excerpt}
                      </p>
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <Badge variant="outline" className="text-xs px-2 py-0.5">{post.category}</Badge>
                        <span className="whitespace-nowrap">{post.readTime} {t('blog.readTime')}</span>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export const Route = createFileRoute('/(marketing)/blog/$slug')({
  component: () => (
    <MarketingLayout>
      <BlogPostPage />
    </MarketingLayout>
  ),
})
