import { createFileRoute, Link } from '@tanstack/react-router'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { IconArrowLeft, IconCalendar, IconUser, IconClock, IconShare, IconHeart } from '@tabler/icons-react'
import { MarketingLayout } from '@/components/layout/marketing-layout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { toast } from 'sonner'

// 模拟博客文章数据
const mockBlogPost = {
  id: '1',
  title: 'Shadcn Admin v2.0 发布：全新的设计系统',
  slug: 'shadcn-admin-v2-release',
  excerpt: '我们很高兴地宣布 Shadcn Admin v2.0 正式发布！这个版本带来了全新的设计系统、更好的性能和更多的组件。',
  content: `
# Shadcn Admin v2.0 发布

我们很高兴地宣布 **Shadcn Admin v2.0** 正式发布！这个版本是我们迄今为止最大的更新，带来了全新的设计系统、更好的性能和更多的组件。

## 🎨 全新的设计系统

v2.0 版本采用了全新的设计语言，更加现代化和一致性：

- **统一的颜色系统**：基于 OKLCH 颜色空间的全新调色板
- **改进的排版**：更好的字体层次和可读性
- **增强的组件**：所有组件都经过重新设计和优化
- **暗色模式优化**：更好的暗色模式体验

## ⚡ 性能提升

我们在性能方面做了大量优化：

- **更快的加载速度**：减少了 30% 的包体积
- **更好的渲染性能**：优化了组件渲染逻辑
- **懒加载支持**：支持组件和路由的懒加载
- **Tree Shaking**：更好的 Tree Shaking 支持

## 🧩 新增组件

v2.0 版本新增了多个实用组件：

- **数据表格**：功能强大的数据表格组件
- **图表组件**：基于 Recharts 的图表组件库
- **文件上传**：支持拖拽的文件上传组件
- **富文本编辑器**：集成的富文本编辑器

## 🔧 开发体验改进

我们也改进了开发体验：

- **更好的 TypeScript 支持**：完整的类型定义
- **改进的文档**：更详细的文档和示例
- **开发工具**：新的开发工具和调试功能
- **测试支持**：内置的测试工具和示例

## 🚀 如何升级

升级到 v2.0 非常简单：

\`\`\`bash
npm install shadcn-admin@latest
\`\`\`

详细的升级指南请参考我们的[升级文档](/docs/upgrade)。

## 🙏 致谢

感谢所有为这个版本做出贡献的开发者和社区成员。你们的反馈和贡献让 Shadcn Admin 变得更好！

---

如果你有任何问题或建议，欢迎在 [GitHub](https://github.com/shadcn-admin/shadcn-admin) 上提交 Issue 或 PR。
  `,
  author: {
    name: '张三',
    avatar: '/images/avatar-1.jpg',
    bio: 'Shadcn Admin 核心开发者'
  },
  publishedAt: '2024-01-15',
  readTime: 5,
  category: '产品更新',
  tags: ['发布', '设计系统', 'React'],
  featured: true
}

// 模拟相关文章
const relatedPosts = [
  {
    id: '2',
    title: '如何使用 React Hook Form 构建高性能表单',
    slug: 'react-hook-form-guide',
    excerpt: '学习如何使用 React Hook Form 和 Zod 验证来构建高性能、类型安全的表单组件。',
    category: '技术教程',
    readTime: 8
  },
  {
    id: '3',
    title: 'Tailwind CSS 最佳实践：构建可维护的样式系统',
    slug: 'tailwind-css-best-practices',
    excerpt: '探索 Tailwind CSS 的最佳实践，学习如何构建可维护、可扩展的样式系统。',
    category: '技术教程',
    readTime: 6
  }
]

// 模拟评论数据
const mockComments = [
  {
    id: '1',
    author: '李四',
    content: '太棒了！新的设计系统看起来很不错，期待尝试新功能。',
    publishedAt: '2024-01-16',
    avatar: '/images/avatar-2.jpg'
  },
  {
    id: '2',
    author: '王五',
    content: '性能提升很明显，升级后页面加载速度确实快了很多。感谢团队的努力！',
    publishedAt: '2024-01-16',
    avatar: '/images/avatar-3.jpg'
  }
]

function BlogPostPage() {
  const { t } = useTranslation()
  const { slug } = Route.useParams()
  const [commentForm, setCommentForm] = useState({
    name: '',
    email: '',
    comment: ''
  })

  const handleCommentSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // 这里应该调用 API 提交评论
    toast.success(t('blog.commentSuccess'))
    setCommentForm({ name: '', email: '', comment: '' })
  }

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: mockBlogPost.title,
        text: mockBlogPost.excerpt,
        url: window.location.href,
      })
    } else {
      // 复制链接到剪贴板
      navigator.clipboard.writeText(window.location.href)
      toast.success('链接已复制到剪贴板')
    }
  }

  return (
    <div className="container py-12">
      {/* 返回按钮 */}
      <div className="mb-8">
        <Button variant="ghost" asChild>
          <Link to="/blog">
            <IconArrowLeft className="h-4 w-4 mr-2" />
            {t('blog.backToBlog')}
          </Link>
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-12">
        {/* 主要内容 */}
        <div className="lg:col-span-3">
          <article>
            {/* 文章头部 */}
            <header className="mb-8">
              <div className="flex items-center gap-2 mb-4">
                <Badge variant="secondary">{mockBlogPost.category}</Badge>
                {mockBlogPost.featured && <Badge variant="outline">特色</Badge>}
              </div>
              
              <h1 className="text-4xl font-bold tracking-tight mb-4">
                {mockBlogPost.title}
              </h1>
              
              <p className="text-xl text-muted-foreground mb-6">
                {mockBlogPost.excerpt}
              </p>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-6 text-sm text-muted-foreground">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                      <IconUser className="h-4 w-4" />
                    </div>
                    <div>
                      <div className="font-medium text-foreground">{mockBlogPost.author.name}</div>
                      <div>{mockBlogPost.author.bio}</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-1">
                    <IconCalendar className="h-4 w-4" />
                    <span>{mockBlogPost.publishedAt}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <IconClock className="h-4 w-4" />
                    <span>{mockBlogPost.readTime} {t('blog.readTime')}</span>
                  </div>
                </div>
                
                <Button variant="outline" size="sm" onClick={handleShare}>
                  <IconShare className="h-4 w-4 mr-2" />
                  {t('blog.shareArticle')}
                </Button>
              </div>
            </header>

            {/* 文章内容 */}
            <div className="prose prose-lg max-w-none mb-12">
              <div className="whitespace-pre-wrap">{mockBlogPost.content}</div>
            </div>

            {/* 标签 */}
            <div className="mb-8">
              <h3 className="text-sm font-medium mb-3">{t('blog.tags')}</h3>
              <div className="flex flex-wrap gap-2">
                {mockBlogPost.tags.map((tag) => (
                  <Badge key={tag} variant="outline">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>

            <Separator className="mb-8" />

            {/* 评论区 */}
            <div>
              <h3 className="text-2xl font-bold mb-6">{t('blog.comments')}</h3>
              
              {/* 评论表单 */}
              <Card className="mb-8">
                <CardHeader>
                  <CardTitle>{t('blog.leaveComment')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleCommentSubmit} className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="name">{t('blog.yourName')}</Label>
                        <Input
                          id="name"
                          value={commentForm.name}
                          onChange={(e) => setCommentForm(prev => ({ ...prev, name: e.target.value }))}
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="email">{t('blog.yourEmail')}</Label>
                        <Input
                          id="email"
                          type="email"
                          value={commentForm.email}
                          onChange={(e) => setCommentForm(prev => ({ ...prev, email: e.target.value }))}
                          required
                        />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="comment">{t('blog.yourComment')}</Label>
                      <Textarea
                        id="comment"
                        rows={4}
                        value={commentForm.comment}
                        onChange={(e) => setCommentForm(prev => ({ ...prev, comment: e.target.value }))}
                        required
                      />
                    </div>
                    <Button type="submit">
                      {t('blog.submitComment')}
                    </Button>
                  </form>
                </CardContent>
              </Card>

              {/* 评论列表 */}
              <div className="space-y-6">
                {mockComments.map((comment) => (
                  <div key={comment.id} className="flex gap-4">
                    <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                      <IconUser className="h-5 w-5" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <span className="font-medium">{comment.author}</span>
                        <span className="text-sm text-muted-foreground">{comment.publishedAt}</span>
                      </div>
                      <p className="text-muted-foreground">{comment.content}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </article>
        </div>

        {/* 侧边栏 */}
        <div className="lg:col-span-1">
          <div className="sticky top-8 space-y-8">
            {/* 相关文章 */}
            <Card>
              <CardHeader>
                <CardTitle>{t('blog.relatedPosts')}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {relatedPosts.map((post) => (
                  <div key={post.id}>
                    <h4 className="font-medium mb-2">
                      <Link to={`/blog/${post.slug}`} className="hover:text-primary transition-colors">
                        {post.title}
                      </Link>
                    </h4>
                    <p className="text-sm text-muted-foreground mb-2 line-clamp-2">
                      {post.excerpt}
                    </p>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <Badge variant="outline" className="text-xs">{post.category}</Badge>
                      <span>{post.readTime} {t('blog.readTime')}</span>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}

export const Route = createFileRoute('/(marketing)/blog/[slug]')({
  component: () => (
    <MarketingLayout>
      <BlogPostPage />
    </MarketingLayout>
  ),
  meta: ({ params }) => [
    {
      title: `${mockBlogPost.title} - Shadcn Admin Blog`,
    },
    {
      name: 'description',
      content: mockBlogPost.excerpt,
    },
    {
      name: 'keywords',
      content: mockBlogPost.tags.join(', '),
    },
  ],
})
