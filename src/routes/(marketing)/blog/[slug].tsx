import { createFile<PERSON>out<PERSON>, <PERSON> } from '@tanstack/react-router'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { IconArrowLeft, IconCalendar, IconUser, IconClock, IconShare } from '@tabler/icons-react'
import { MarketingLayout } from '@/components/layout/marketing-layout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { toast } from 'sonner'
import { getBlogPostBySlug, getRelatedPosts, getBlogComments } from '@/data/blog-posts'

function BlogPostPage() {
  const { t } = useTranslation()
  const { slug } = Route.useParams()
  const [commentForm, setCommentForm] = useState({
    name: '',
    email: '',
    comment: ''
  })

  // 获取博客文章数据
  const blogPost = getBlogPostBySlug(slug)
  const relatedPosts = blogPost ? getRelatedPosts(blogPost.id) : []
  const comments = blogPost ? getBlogComments(blogPost.id) : []

  // 如果文章不存在，显示404
  if (!blogPost) {
    return (
      <div className="container py-24 text-center">
        <h1 className="text-2xl font-bold mb-4">文章未找到</h1>
        <p className="text-muted-foreground mb-8">您访问的文章不存在或已被删除。</p>
        <Button asChild>
          <Link to="/blog">返回博客</Link>
        </Button>
      </div>
    )
  }

  const handleCommentSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // 这里应该调用 API 提交评论
    toast.success(t('blog.commentSuccess'))
    setCommentForm({ name: '', email: '', comment: '' })
  }

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: blogPost.title,
        text: blogPost.excerpt,
        url: window.location.href,
      })
    } else {
      // 复制链接到剪贴板
      navigator.clipboard.writeText(window.location.href)
      toast.success('链接已复制到剪贴板')
    }
  }

  return (
    <div className="container py-12">
      {/* 返回按钮 */}
      <div className="mb-8">
        <Button variant="ghost" asChild>
          <Link to="/blog">
            <IconArrowLeft className="h-4 w-4 mr-2" />
            {t('blog.backToBlog')}
          </Link>
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-12">
        {/* 主要内容 */}
        <div className="lg:col-span-3">
          <article>
            {/* 文章头部 */}
            <header className="mb-8">
              <div className="flex items-center gap-2 mb-4">
                <Badge variant="secondary">{blogPost.category}</Badge>
                {blogPost.featured && <Badge variant="outline">特色</Badge>}
              </div>

              <h1 className="text-4xl font-bold tracking-tight mb-4">
                {blogPost.title}
              </h1>

              <p className="text-xl text-muted-foreground mb-6">
                {blogPost.excerpt}
              </p>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-6 text-sm text-muted-foreground">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                      <IconUser className="h-4 w-4" />
                    </div>
                    <div>
                      <div className="font-medium text-foreground">{blogPost.author.name}</div>
                      <div>{blogPost.author.bio}</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-1">
                    <IconCalendar className="h-4 w-4" />
                    <span>{blogPost.publishedAt}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <IconClock className="h-4 w-4" />
                    <span>{blogPost.readTime} {t('blog.readTime')}</span>
                  </div>
                </div>

                <Button variant="outline" size="sm" onClick={handleShare}>
                  <IconShare className="h-4 w-4 mr-2" />
                  {t('blog.shareArticle')}
                </Button>
              </div>
            </header>

            {/* 文章内容 */}
            <div className="prose prose-lg max-w-none mb-12">
              <div className="whitespace-pre-wrap">{blogPost.content}</div>
            </div>

            {/* 标签 */}
            <div className="mb-8">
              <h3 className="text-sm font-medium mb-3">{t('blog.tags')}</h3>
              <div className="flex flex-wrap gap-2">
                {blogPost.tags.map((tag: string) => (
                  <Badge key={tag} variant="outline">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>

            <Separator className="mb-8" />

            {/* 评论区 */}
            <div>
              <h3 className="text-2xl font-bold mb-6">{t('blog.comments')}</h3>

              {/* 评论表单 */}
              <Card className="mb-8">
                <CardHeader>
                  <CardTitle>{t('blog.leaveComment')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleCommentSubmit} className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="name">{t('blog.yourName')}</Label>
                        <Input
                          id="name"
                          value={commentForm.name}
                          onChange={(e) => setCommentForm(prev => ({ ...prev, name: e.target.value }))}
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="email">{t('blog.yourEmail')}</Label>
                        <Input
                          id="email"
                          type="email"
                          value={commentForm.email}
                          onChange={(e) => setCommentForm(prev => ({ ...prev, email: e.target.value }))}
                          required
                        />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="comment">{t('blog.yourComment')}</Label>
                      <Textarea
                        id="comment"
                        rows={4}
                        value={commentForm.comment}
                        onChange={(e) => setCommentForm(prev => ({ ...prev, comment: e.target.value }))}
                        required
                      />
                    </div>
                    <Button type="submit">
                      {t('blog.submitComment')}
                    </Button>
                  </form>
                </CardContent>
              </Card>

              {/* 评论列表 */}
              <div className="space-y-6">
                {comments.map((comment) => (
                  <div key={comment.id} className="flex gap-4">
                    <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                      <IconUser className="h-5 w-5" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <span className="font-medium">{comment.author}</span>
                        <span className="text-sm text-muted-foreground">{comment.publishedAt}</span>
                      </div>
                      <p className="text-muted-foreground">{comment.content}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </article>
        </div>

        {/* 侧边栏 */}
        <div className="lg:col-span-1">
          <div className="sticky top-8 space-y-8">
            {/* 相关文章 */}
            <Card>
              <CardHeader>
                <CardTitle>{t('blog.relatedPosts')}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {relatedPosts.map((post) => (
                  <div key={post.id}>
                    <h4 className="font-medium mb-2">
                      <a href={`/blog/${post.slug}`} className="hover:text-primary transition-colors">
                        {post.title}
                      </a>
                    </h4>
                    <p className="text-sm text-muted-foreground mb-2 line-clamp-2">
                      {post.excerpt}
                    </p>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <Badge variant="outline" className="text-xs">{post.category}</Badge>
                      <span>{post.readTime} {t('blog.readTime')}</span>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}

export const Route = createFileRoute('/(marketing)/blog/[slug]')({
  component: () => (
    <MarketingLayout>
      <BlogPostPage />
    </MarketingLayout>
  ),
})
