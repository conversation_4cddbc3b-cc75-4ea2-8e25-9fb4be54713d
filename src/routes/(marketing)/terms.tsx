import { createFileRoute } from '@tanstack/react-router'
import { useTranslation } from 'react-i18next'
import { IconScale, IconUsers, IconFileText, IconUser, IconCopyright, IconShield, IconX, IconGavel, IconMail } from '@tabler/icons-react'
import { MarketingLayout } from '@/components/layout/marketing-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'

function TermsPage() {
  const { t } = useTranslation()

  const sections = [
    {
      id: 'acceptance',
      icon: IconUsers,
      title: t('terms.acceptance'),
      content: t('terms.acceptanceContent')
    },
    {
      id: 'serviceDescription',
      icon: IconFileText,
      title: t('terms.serviceDescription'),
      content: t('terms.descriptionContent')
    },
    {
      id: 'userResponsibilities',
      icon: IconUser,
      title: t('terms.userResponsibilities'),
      content: t('terms.responsibilitiesContent')
    },
    {
      id: 'intellectualProperty',
      icon: IconCopyright,
      title: t('terms.intellectualProperty'),
      content: t('terms.propertyContent')
    },
    {
      id: 'limitation',
      icon: IconShield,
      title: t('terms.limitation'),
      content: t('terms.limitationContent')
    },
    {
      id: 'termination',
      icon: IconX,
      title: t('terms.termination'),
      content: t('terms.terminationContent')
    },
    {
      id: 'governingLaw',
      icon: IconGavel,
      title: t('terms.governingLaw'),
      content: t('terms.lawContent')
    }
  ]

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="container py-24">
        <div className="mx-auto max-w-4xl text-center">
          <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6">
            <IconScale className="h-8 w-8 text-primary" />
          </div>
          <h1 className="text-4xl font-bold tracking-tight sm:text-5xl mb-6">
            {t('terms.title')}
          </h1>
          <p className="text-lg text-muted-foreground mb-4">
            {t('terms.lastUpdated')}: 2024年1月15日
          </p>
          <p className="text-muted-foreground">
            请仔细阅读这些服务条款，它们规定了您使用 Shadcn Admin 服务的权利和义务。
          </p>
        </div>
      </section>

      {/* Important Notice */}
      <section className="container py-8">
        <div className="max-w-4xl mx-auto">
          <Alert>
            <IconScale className="h-4 w-4" />
            <AlertDescription>
              <strong>重要提示：</strong>通过使用我们的服务，您表示同意受这些条款的约束。如果您不同意这些条款，请不要使用我们的服务。
            </AlertDescription>
          </Alert>
        </div>
      </section>

      {/* Table of Contents */}
      <section className="container py-8">
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle>目录</CardTitle>
              <CardDescription>
                点击下面的链接快速跳转到相应章节
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {sections.map((section) => (
                  <a
                    key={section.id}
                    href={`#${section.id}`}
                    className="flex items-center gap-3 p-3 rounded-lg hover:bg-muted transition-colors"
                  >
                    <section.icon className="h-5 w-5 text-primary" />
                    <span className="font-medium">{section.title}</span>
                  </a>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Content Sections */}
      <section className="container py-8">
        <div className="max-w-4xl mx-auto space-y-12">
          {sections.map((section, index) => (
            <div key={section.id} id={section.id}>
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-3">
                    <section.icon className="h-6 w-6 text-primary" />
                    {section.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="prose prose-lg max-w-none">
                    <p className="text-muted-foreground leading-relaxed">
                      {section.content}
                    </p>
                  </div>
                </CardContent>
              </Card>
              {index < sections.length - 1 && <Separator className="my-8" />}
            </div>
          ))}

          {/* Additional Terms */}
          <div id="additionalTerms">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <IconFileText className="h-6 w-6 text-primary" />
                  附加条款
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div>
                    <h4 className="font-medium mb-3">开源许可</h4>
                    <p className="text-muted-foreground mb-4">
                      Shadcn Admin 的核心代码基于 MIT 许可证开源。您可以自由使用、修改和分发代码，但需要保留原始版权声明。
                    </p>
                    <div className="p-4 bg-muted rounded-lg">
                      <code className="text-sm">
                        MIT License - Copyright (c) 2024 Shadcn Admin
                      </code>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium mb-3">第三方服务</h4>
                    <p className="text-muted-foreground">
                      我们的服务可能集成第三方服务和组件。这些服务有自己的条款和条件，您在使用时需要遵守相应的规定。
                    </p>
                  </div>

                  <div>
                    <h4 className="font-medium mb-3">数据备份</h4>
                    <p className="text-muted-foreground">
                      虽然我们努力保护您的数据，但建议您定期备份重要数据。我们不对数据丢失承担责任。
                    </p>
                  </div>

                  <div>
                    <h4 className="font-medium mb-3">服务可用性</h4>
                    <p className="text-muted-foreground">
                      我们努力保持服务的高可用性，但不保证服务永远不会中断。我们会在计划维护前提前通知用户。
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Contact Section */}
          <div id="contact">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <IconMail className="h-6 w-6 text-primary" />
                  {t('terms.contactUs')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-muted-foreground">
                    如果您对这些服务条款有任何疑问，请通过以下方式联系我们：
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-medium mb-2">法务邮箱</h4>
                      <p className="text-muted-foreground"><EMAIL></p>
                    </div>
                    <div>
                      <h4 className="font-medium mb-2">客服邮箱</h4>
                      <p className="text-muted-foreground"><EMAIL></p>
                    </div>
                    <div>
                      <h4 className="font-medium mb-2">邮寄地址</h4>
                      <p className="text-muted-foreground">
                        北京市朝阳区建国门外大街1号<br />
                        Shadcn Admin 法务部门
                      </p>
                    </div>
                    <div>
                      <h4 className="font-medium mb-2">电话</h4>
                      <p className="text-muted-foreground">+86 138-0013-8000</p>
                    </div>
                  </div>
                  <div className="pt-4">
                    <Button asChild>
                      <a href="/contact">联系我们</a>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Updates Section */}
      <section className="bg-muted/50 py-16">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-2xl font-bold mb-4">条款更新</h2>
            <p className="text-muted-foreground mb-6">
              我们保留随时修改这些条款的权利。重大变更时，我们会提前30天通知您。继续使用服务即表示您接受修改后的条款。
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="outline" asChild>
                <a href="/changelog">查看更新历史</a>
              </Button>
              <Button asChild>
                <a href="/privacy">隐私政策</a>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export const Route = createFileRoute('/(marketing)/terms')({
  component: () => (
    <MarketingLayout>
      <TermsPage />
    </MarketingLayout>
  ),
})
