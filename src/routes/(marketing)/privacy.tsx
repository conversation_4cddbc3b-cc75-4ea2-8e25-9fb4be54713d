import { createFileRoute } from '@tanstack/react-router'
import { useTranslation } from 'react-i18next'
import { IconShield, IconEye, IconDatabase, IconShare, IconLock, IconUser, IconCookie, IconMail } from '@tabler/icons-react'
import { MarketingLayout } from '@/components/layout/marketing-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Button } from '@/components/ui/button'

function PrivacyPage() {
  const { t } = useTranslation()

  const sections = [
    {
      id: 'introduction',
      icon: IconShield,
      title: t('privacy.introduction'),
      content: t('privacy.introContent')
    },
    {
      id: 'dataCollection',
      icon: IconDatabase,
      title: t('privacy.dataCollection'),
      content: t('privacy.collectionContent')
    },
    {
      id: 'dataUsage',
      icon: IconEye,
      title: t('privacy.dataUsage'),
      content: t('privacy.usageContent')
    },
    {
      id: 'dataSharing',
      icon: IconShare,
      title: t('privacy.dataSharing'),
      content: t('privacy.sharingContent')
    },
    {
      id: 'dataSecurity',
      icon: IconLock,
      title: t('privacy.dataSecurity'),
      content: t('privacy.securityContent')
    },
    {
      id: 'userRights',
      icon: IconUser,
      title: t('privacy.userRights'),
      content: t('privacy.rightsContent')
    }
  ]

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="container py-24">
        <div className="mx-auto max-w-4xl text-center">
          <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6">
            <IconShield className="h-8 w-8 text-primary" />
          </div>
          <h1 className="text-4xl font-bold tracking-tight sm:text-5xl mb-6">
            {t('privacy.title')}
          </h1>
          <p className="text-lg text-muted-foreground mb-4">
            {t('privacy.lastUpdated')}: 2024年1月15日
          </p>
          <p className="text-muted-foreground">
            我们重视您的隐私，并致力于保护您的个人信息。本隐私政策详细说明了我们如何收集、使用和保护您的数据。
          </p>
        </div>
      </section>

      {/* Table of Contents */}
      <section className="container py-8">
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle>目录</CardTitle>
              <CardDescription>
                点击下面的链接快速跳转到相应章节
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {sections.map((section) => (
                  <a
                    key={section.id}
                    href={`#${section.id}`}
                    className="flex items-center gap-3 p-3 rounded-lg hover:bg-muted transition-colors"
                  >
                    <section.icon className="h-5 w-5 text-primary" />
                    <span className="font-medium">{section.title}</span>
                  </a>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Content Sections */}
      <section className="container py-8">
        <div className="max-w-4xl mx-auto space-y-12">
          {sections.map((section, index) => (
            <div key={section.id} id={section.id}>
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-3">
                    <section.icon className="h-6 w-6 text-primary" />
                    {section.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="prose prose-lg max-w-none">
                    <p className="text-muted-foreground leading-relaxed">
                      {section.content}
                    </p>
                  </div>
                </CardContent>
              </Card>
              {index < sections.length - 1 && <Separator className="my-8" />}
            </div>
          ))}

          {/* Cookie Policy Section */}
          <div id="cookiePolicy">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <IconCookie className="h-6 w-6 text-primary" />
                  {t('privacy.cookiePolicy')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-muted-foreground">
                    我们使用 Cookie 和类似技术来改善您的浏览体验：
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-4 border rounded-lg">
                      <h4 className="font-medium mb-2">必要 Cookie</h4>
                      <p className="text-sm text-muted-foreground">
                        这些 Cookie 对网站正常运行是必需的，无法禁用。
                      </p>
                    </div>
                    <div className="p-4 border rounded-lg">
                      <h4 className="font-medium mb-2">分析 Cookie</h4>
                      <p className="text-sm text-muted-foreground">
                        帮助我们了解用户如何使用网站，以便改进用户体验。
                      </p>
                    </div>
                    <div className="p-4 border rounded-lg">
                      <h4 className="font-medium mb-2">功能 Cookie</h4>
                      <p className="text-sm text-muted-foreground">
                        记住您的偏好设置，如语言和主题选择。
                      </p>
                    </div>
                    <div className="p-4 border rounded-lg">
                      <h4 className="font-medium mb-2">营销 Cookie</h4>
                      <p className="text-sm text-muted-foreground">
                        用于向您展示相关的广告和营销内容。
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Contact Section */}
          <div id="contact">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <IconMail className="h-6 w-6 text-primary" />
                  {t('privacy.contactUs')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-muted-foreground">
                    如果您对本隐私政策有任何疑问或需要行使您的权利，请通过以下方式联系我们：
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-medium mb-2">邮箱地址</h4>
                      <p className="text-muted-foreground"><EMAIL></p>
                    </div>
                    <div>
                      <h4 className="font-medium mb-2">邮寄地址</h4>
                      <p className="text-muted-foreground">
                        北京市朝阳区建国门外大街1号<br />
                        Shadcn Admin 隐私保护部门
                      </p>
                    </div>
                  </div>
                  <div className="pt-4">
                    <Button asChild>
                      <a href="/contact">联系我们</a>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Updates Section */}
      <section className="bg-muted/50 py-16">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-2xl font-bold mb-4">政策更新</h2>
            <p className="text-muted-foreground mb-6">
              我们可能会不时更新本隐私政策。重大变更时，我们会通过邮件或网站通知您。
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="outline" asChild>
                <a href="/changelog">查看更新历史</a>
              </Button>
              <Button asChild>
                <a href="/contact">联系我们</a>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export const Route = createFileRoute('/(marketing)/privacy')({
  component: () => (
    <MarketingLayout>
      <PrivacyPage />
    </MarketingLayout>
  ),
  meta: () => [
    {
      title: 'Privacy Policy - Shadcn Admin',
    },
    {
      name: 'description',
      content: 'Learn how Shadcn Admin collects, uses, and protects your personal information. Our commitment to your privacy.',
    },
    {
      name: 'keywords',
      content: 'privacy policy, data protection, personal information, cookies, GDPR',
    },
  ],
})
