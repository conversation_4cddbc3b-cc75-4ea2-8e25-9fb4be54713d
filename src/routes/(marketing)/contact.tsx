import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { IconMail, IconPhone, IconMapPin, IconClock, IconGithub, IconTwitter, IconLinkedin } from '@tabler/icons-react'
import { MarketingLayout } from '@/components/layout/marketing-layout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { toast } from 'sonner'

// 表单验证 schema
const contactFormSchema = z.object({
  name: z.string().min(2, '姓名至少需要2个字符'),
  email: z.string().email('请输入有效的邮箱地址'),
  subject: z.string().min(5, '主题至少需要5个字符'),
  message: z.string().min(10, '消息内容至少需要10个字符'),
})

type ContactFormData = z.infer<typeof contactFormSchema>

// 联系信息
const contactInfo = [
  {
    icon: IconMail,
    label: 'email',
    value: '<EMAIL>',
    href: 'mailto:<EMAIL>'
  },
  {
    icon: IconPhone,
    label: 'phone',
    value: '+86 138-0013-8000',
    href: 'tel:+8613800138000'
  },
  {
    icon: IconMapPin,
    label: 'address',
    value: '北京市朝阳区建国门外大街1号',
    href: null
  },
  {
    icon: IconClock,
    label: 'businessHours',
    value: '周一至周五 9:00 - 18:00',
    href: null
  }
]

// 社交媒体链接
const socialLinks = [
  {
    icon: IconGithub,
    label: 'GitHub',
    href: 'https://github.com/shadcn-admin'
  },
  {
    icon: IconTwitter,
    label: 'Twitter',
    href: 'https://twitter.com/shadcn_admin'
  },
  {
    icon: IconLinkedin,
    label: 'LinkedIn',
    href: 'https://linkedin.com/company/shadcn-admin'
  }
]

function ContactPage() {
  const { t } = useTranslation()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const form = useForm<ContactFormData>({
    resolver: zodResolver(contactFormSchema),
    defaultValues: {
      name: '',
      email: '',
      subject: '',
      message: '',
    },
  })

  const onSubmit = async (data: ContactFormData) => {
    setIsSubmitting(true)
    try {
      // 这里应该调用 API 发送邮件
      await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟 API 调用
      toast.success(t('contact.messageSent'))
      form.reset()
    } catch (error) {
      toast.error(t('contact.messageError'))
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="container py-24">
        <div className="mx-auto max-w-4xl text-center">
          <h1 className="text-4xl font-bold tracking-tight sm:text-5xl mb-6">
            {t('contact.title')}
          </h1>
          <p className="text-xl text-muted-foreground">
            {t('contact.subtitle')}
          </p>
        </div>
      </section>

      {/* Contact Section */}
      <section className="container py-16">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
          {/* 联系信息 */}
          <div>
            <h2 className="text-2xl font-bold mb-8">{t('contact.getInTouch')}</h2>
            
            <div className="space-y-6 mb-8">
              {contactInfo.map((info, index) => (
                <div key={index} className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                    <info.icon className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-medium mb-1">{t(`contact.${info.label}`)}</h3>
                    {info.href ? (
                      <a 
                        href={info.href} 
                        className="text-muted-foreground hover:text-primary transition-colors"
                      >
                        {info.value}
                      </a>
                    ) : (
                      <p className="text-muted-foreground">{info.value}</p>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {/* 响应时间说明 */}
            <Card className="mb-8">
              <CardContent className="pt-6">
                <p className="text-sm text-muted-foreground">
                  {t('contact.responseTime')}
                </p>
              </CardContent>
            </Card>

            {/* 社交媒体 */}
            <div>
              <h3 className="font-medium mb-4">{t('contact.followUs')}</h3>
              <div className="flex gap-3">
                {socialLinks.map((social, index) => (
                  <Button key={index} variant="outline" size="sm" asChild>
                    <a href={social.href} target="_blank" rel="noopener noreferrer">
                      <social.icon className="h-4 w-4 mr-2" />
                      {social.label}
                    </a>
                  </Button>
                ))}
              </div>
            </div>
          </div>

          {/* 联系表单 */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle>{t('contact.sendMessage')}</CardTitle>
                <CardDescription>
                  填写下面的表单，我们会尽快回复您。
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t('contact.yourName')}</FormLabel>
                          <FormControl>
                            <Input placeholder="请输入您的姓名" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t('contact.yourEmail')}</FormLabel>
                          <FormControl>
                            <Input 
                              type="email" 
                              placeholder="请输入您的邮箱地址" 
                              {...field} 
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="subject"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t('contact.subject')}</FormLabel>
                          <FormControl>
                            <Input placeholder="请输入邮件主题" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="message"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t('contact.message')}</FormLabel>
                          <FormControl>
                            <Textarea 
                              placeholder="请详细描述您的问题或建议..."
                              rows={6}
                              {...field} 
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <Button 
                      type="submit" 
                      className="w-full" 
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? '发送中...' : t('contact.sendButton')}
                    </Button>
                  </form>
                </Form>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="bg-muted/50 py-16">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-12">常见问题</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <Card>
                <CardHeader>
                  <CardTitle>如何开始使用 Shadcn Admin？</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    您可以访问我们的文档页面，按照快速开始指南进行安装和配置。我们提供了详细的步骤说明和示例代码。
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>是否提供技术支持？</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    是的，我们为所有用户提供社区支持。付费用户还可以享受优先技术支持和专属客户服务。
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>可以定制开发吗？</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    我们为企业用户提供定制开发服务，包括组件定制、主题设计和功能扩展等。
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>如何贡献代码？</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    欢迎通过 GitHub 提交 Pull Request 或 Issue。请先阅读我们的贡献指南了解详细流程。
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export const Route = createFileRoute('/(marketing)/contact')({
  component: () => (
    <MarketingLayout>
      <ContactPage />
    </MarketingLayout>
  ),
  meta: () => [
    {
      title: 'Contact Us - Shadcn Admin',
    },
    {
      name: 'description',
      content: 'Get in touch with the Shadcn Admin team. We are here to help with any questions or feedback.',
    },
    {
      name: 'keywords',
      content: 'contact, support, help, feedback, shadcn admin',
    },
  ],
})
