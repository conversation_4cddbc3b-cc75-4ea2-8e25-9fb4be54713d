import { createFileRoute } from '@tanstack/react-router'
import { MarketingLayout } from '@/components/layout/marketing-layout'
import { useTranslation } from 'react-i18next'
import { IconCheck } from '@tabler/icons-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

function PricingPage() {
  const { t } = useTranslation()

  const plans = [
    {
      name: '免费版',
      price: '¥0',
      period: '永久免费',
      description: '适合个人项目和小团队',
      features: [
        '基础组件库',
        '响应式设计',
        '暗色模式',
        '社区支持',
        '开源代码',
      ],
      popular: false,
    },
    {
      name: '专业版',
      price: '¥299',
      period: '每月',
      description: '适合成长中的团队',
      features: [
        '所有免费版功能',
        '高级组件',
        '优先技术支持',
        '定制主题',
        '团队协作工具',
        '高级图表组件',
      ],
      popular: true,
    },
    {
      name: '企业版',
      price: '¥999',
      period: '每月',
      description: '适合大型企业',
      features: [
        '所有专业版功能',
        '专属客户经理',
        '定制开发服务',
        '私有部署',
        'SLA 保障',
        '培训服务',
        '源码授权',
      ],
      popular: false,
    },
  ]

  return (
    <div className="container py-24">
      <div className="mx-auto max-w-4xl text-center mb-16">
        <h1 className="text-4xl font-bold tracking-tight sm:text-5xl mb-4">
          选择适合您的方案
        </h1>
        <p className="text-xl text-muted-foreground">
          从免费开源版本开始，随着业务增长升级到专业版
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
        {plans.map((plan, index) => (
          <Card key={index} className={`relative ${plan.popular ? 'border-primary shadow-lg scale-105' : ''}`}>
            {plan.popular && (
              <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                最受欢迎
              </Badge>
            )}
            <CardHeader className="text-center">
              <CardTitle className="text-2xl">{plan.name}</CardTitle>
              <div className="mt-4">
                <span className="text-4xl font-bold">{plan.price}</span>
                <span className="text-muted-foreground">/{plan.period}</span>
              </div>
              <CardDescription className="mt-2">{plan.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3 mb-6">
                {plan.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-center">
                    <IconCheck className="h-4 w-4 text-green-500 mr-3 flex-shrink-0" />
                    <span className="text-sm">{feature}</span>
                  </li>
                ))}
              </ul>
              <Button 
                className="w-full" 
                variant={plan.popular ? 'default' : 'outline'}
              >
                {plan.name === '免费版' ? '立即下载' : '开始试用'}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="mt-16 text-center">
        <p className="text-muted-foreground mb-4">
          需要定制方案？
        </p>
        <Button variant="outline">
          联系销售团队
        </Button>
      </div>
    </div>
  )
}

export const Route = createFileRoute('/(marketing)/pricing')({
  component: () => (
    <MarketingLayout>
      <PricingPage />
    </MarketingLayout>
  ),
  meta: () => [
    {
      title: 'Pricing - Shadcn Admin',
    },
    {
      name: 'description',
      content: 'Choose the perfect plan for your team. Start with our free open source version.',
    },
  ],
})
