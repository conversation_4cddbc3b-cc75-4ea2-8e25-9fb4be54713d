import { createFileRoute } from '@tanstack/react-router'
import { useTranslation } from 'react-i18next'
import { IconBulb, IconHeart, IconAward, IconUsers, IconGithub, IconTwitter, IconLinkedin } from '@tabler/icons-react'
import { MarketingLayout } from '@/components/layout/marketing-layout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

// 团队成员数据
const teamMembers = [
  {
    id: '1',
    name: '张三',
    role: '创始人 & CEO',
    bio: '拥有10年前端开发经验，专注于构建优秀的开发者工具和用户体验。',
    avatar: '/images/avatar-1.jpg',
    social: {
      github: 'https://github.com/zhangsan',
      twitter: 'https://twitter.com/zhangsan',
      linkedin: 'https://linkedin.com/in/zhangsan'
    }
  },
  {
    id: '2',
    name: '李四',
    role: '技术总监',
    bio: '全栈开发专家，在 React 生态系统和现代 Web 技术方面有深厚造诣。',
    avatar: '/images/avatar-2.jpg',
    social: {
      github: 'https://github.com/lisi',
      twitter: 'https://twitter.com/lisi'
    }
  },
  {
    id: '3',
    name: '王五',
    role: '设计总监',
    bio: 'UI/UX 设计专家，致力于创造直观、美观的用户界面和体验。',
    avatar: '/images/avatar-3.jpg',
    social: {
      github: 'https://github.com/wangwu',
      linkedin: 'https://linkedin.com/in/wangwu'
    }
  },
  {
    id: '4',
    name: '赵六',
    role: '社区经理',
    bio: '负责社区建设和开发者关系，帮助用户更好地使用我们的产品。',
    avatar: '/images/avatar-4.jpg',
    social: {
      twitter: 'https://twitter.com/zhaoliu',
      linkedin: 'https://linkedin.com/in/zhaoliu'
    }
  }
]

// 公司统计数据
const stats = [
  { label: '活跃用户', value: '50K+' },
  { label: 'GitHub Stars', value: '15K+' },
  { label: '社区贡献者', value: '200+' },
  { label: '项目使用', value: '1000+' }
]

function AboutPage() {
  const { t } = useTranslation()

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="container py-24">
        <div className="mx-auto max-w-4xl text-center">
          <h1 className="text-4xl font-bold tracking-tight sm:text-6xl mb-6">
            {t('about.title')}
          </h1>
          <p className="text-xl text-muted-foreground mb-8">
            {t('about.subtitle')}
          </p>
        </div>
      </section>

      {/* Stats Section */}
      <section className="container py-16">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-3xl font-bold text-primary mb-2">{stat.value}</div>
              <div className="text-muted-foreground">{stat.label}</div>
            </div>
          ))}
        </div>
      </section>

      {/* Story Section */}
      <section className="container py-16">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-center mb-12">{t('about.ourStory')}</h2>
          <div className="prose prose-lg max-w-none text-center">
            <p className="text-xl text-muted-foreground leading-relaxed">
              {t('about.storyContent')}
            </p>
          </div>
        </div>
      </section>

      {/* Mission & Vision Section */}
      <section className="bg-muted/50 py-16">
        <div className="container">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 max-w-6xl mx-auto">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <IconBulb className="h-6 w-6 text-primary" />
                  {t('about.ourMission')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  {t('about.missionContent')}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <IconAward className="h-6 w-6 text-primary" />
                  {t('about.ourVision')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  {t('about.visionContent')}
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="container py-16">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl font-bold text-center mb-12">{t('about.ourValues')}</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <IconBulb className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-2">{t('about.innovation')}</h3>
              <p className="text-muted-foreground">{t('about.innovationDesc')}</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <IconGithub className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-2">{t('about.openSource')}</h3>
              <p className="text-muted-foreground">{t('about.openSourceDesc')}</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <IconAward className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-2">{t('about.quality')}</h3>
              <p className="text-muted-foreground">{t('about.qualityDesc')}</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <IconUsers className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-2">{t('about.community')}</h3>
              <p className="text-muted-foreground">{t('about.communityDesc')}</p>
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="bg-muted/50 py-16">
        <div className="container">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-12">{t('about.ourTeam')}</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {teamMembers.map((member) => (
                <Card key={member.id} className="text-center">
                  <CardHeader>
                    <div className="w-24 h-24 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                      <IconUsers className="h-12 w-12 text-primary" />
                    </div>
                    <CardTitle>{member.name}</CardTitle>
                    <CardDescription>
                      <Badge variant="secondary">{member.role}</Badge>
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground mb-4">{member.bio}</p>
                    <div className="flex justify-center gap-2">
                      {member.social.github && (
                        <Button variant="ghost" size="sm" asChild>
                          <a href={member.social.github} target="_blank" rel="noopener noreferrer">
                            <IconGithub className="h-4 w-4" />
                          </a>
                        </Button>
                      )}
                      {member.social.twitter && (
                        <Button variant="ghost" size="sm" asChild>
                          <a href={member.social.twitter} target="_blank" rel="noopener noreferrer">
                            <IconTwitter className="h-4 w-4" />
                          </a>
                        </Button>
                      )}
                      {member.social.linkedin && (
                        <Button variant="ghost" size="sm" asChild>
                          <a href={member.social.linkedin} target="_blank" rel="noopener noreferrer">
                            <IconLinkedin className="h-4 w-4" />
                          </a>
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container py-16">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold mb-4">{t('about.joinUs')}</h2>
          <p className="text-xl text-muted-foreground mb-8">
            想要加入我们的团队？我们正在寻找有才华的开发者和设计师。
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" asChild>
              <a href="/contact">联系我们</a>
            </Button>
            <Button variant="outline" size="lg" asChild>
              <a href="https://github.com/shadcn-admin" target="_blank" rel="noopener noreferrer">
                <IconGithub className="h-4 w-4 mr-2" />
                查看 GitHub
              </a>
            </Button>
          </div>
        </div>
      </section>
    </div>
  )
}

export const Route = createFileRoute('/(marketing)/about')({
  component: () => (
    <MarketingLayout>
      <AboutPage />
    </MarketingLayout>
  ),
  meta: () => [
    {
      title: 'About Us - Shadcn Admin',
    },
    {
      name: 'description',
      content: 'Learn about our story, mission, and the team behind Shadcn Admin. We are passionate about building great developer tools.',
    },
    {
      name: 'keywords',
      content: 'about, team, mission, vision, shadcn admin, company',
    },
  ],
})
