import { useTranslation } from 'react-i18next'

export function TestI18n() {
  const { t, i18n } = useTranslation()

  const handleLanguageChange = (lang: string) => {
    i18n.changeLanguage(lang)
  }

  return (
    <div className="p-4 space-y-4">
      <h1 className="text-2xl font-bold">国际化测试 / i18n Test</h1>
      
      <div className="space-x-2">
        <button 
          onClick={() => handleLanguageChange('zh-CN')}
          className="px-4 py-2 bg-blue-500 text-white rounded"
        >
          中文
        </button>
        <button 
          onClick={() => handleLanguageChange('en-US')}
          className="px-4 py-2 bg-green-500 text-white rounded"
        >
          English
        </button>
      </div>

      <div className="space-y-2">
        <p><strong>当前语言 / Current Language:</strong> {i18n.language}</p>
        <p><strong>导航测试:</strong></p>
        <ul className="list-disc list-inside space-y-1">
          <li>{t('navigation.dashboard')}</li>
          <li>{t('navigation.tasks')}</li>
          <li>{t('navigation.users')}</li>
          <li>{t('navigation.settings')}</li>
        </ul>
        
        <p><strong>按钮测试:</strong></p>
        <ul className="list-disc list-inside space-y-1">
          <li>{t('buttons.save')}</li>
          <li>{t('buttons.cancel')}</li>
          <li>{t('buttons.edit')}</li>
          <li>{t('buttons.delete')}</li>
        </ul>
        
        <p><strong>消息测试:</strong></p>
        <ul className="list-disc list-inside space-y-1">
          <li>{t('messages.operationSuccessful')}</li>
          <li>{t('messages.operationFailed')}</li>
          <li>{t('common.loading')}</li>
        </ul>
      </div>
    </div>
  )
}
