{"navigation": {"dashboard": "Dashboard", "tasks": "Tasks", "apps": "Apps", "chats": "Chats", "users": "Users", "settings": "Settings", "helpCenter": "Help Center", "general": "General", "pages": "Pages", "other": "Other", "auth": "<PERSON><PERSON>", "errors": "Errors", "profile": "Profile", "account": "Account", "appearance": "Appearance", "notifications": "Notifications", "display": "Display", "signIn": "Sign In", "signUp": "Sign Up", "signOut": "Sign Out", "forgotPassword": "Forgot Password", "otp": "OTP", "userManagement": "User Management", "unauthorized": "Unauthorized", "forbidden": "Forbidden", "notFound": "Not Found", "internalServerError": "Internal Server Error", "maintenanceError": "Maintenance Error", "securedByClerk": "Secured by Clerk"}, "dashboard": {"title": "Dashboard", "download": "Download", "overview": "Overview", "analytics": "Analytics", "reports": "Reports", "notifications": "Notifications", "customers": "Customers", "products": "Products", "totalRevenue": "Total Revenue", "subscriptions": "Subscriptions", "sales": "Sales", "activeNow": "Active Now", "fromLastMonth": "from last month", "sinceLastHour": "since last hour", "recentSales": "Recent Sales", "salesThisMonth": "You made 265 sales this month."}, "tasks": {"title": "Tasks", "description": "Here's a list of your tasks for this month!", "new": "New Task", "edit": "Edit Task", "delete": "Delete Task", "status": "Status", "priority": "Priority", "label": "Label", "todo": "Todo", "inProgress": "In Progress", "done": "Done", "canceled": "Canceled", "backlog": "Backlog", "low": "Low", "medium": "Medium", "high": "High", "bug": "Bug", "feature": "Feature", "documentation": "Documentation"}, "users": {"title": "Users", "description": "Manage your team members and their account permissions here.", "new": "New User", "edit": "Edit User", "delete": "Delete User", "firstName": "First Name", "lastName": "Last Name", "username": "Username", "email": "Email", "phoneNumber": "Phone Number", "role": "Role", "status": "Status", "createdAt": "Created At", "updatedAt": "Updated At", "active": "Active", "inactive": "Inactive", "invited": "Invited", "suspended": "Suspended", "superadmin": "Super Admin", "admin": "Admin", "cashier": "Cashier", "manager": "Manager"}, "apps": {"title": "App Integrations", "description": "Here's a list of your apps for the integration!", "filterApps": "Filter apps...", "allApps": "All Apps", "connected": "Connected", "notConnected": "Not Connected", "connect": "Connect", "disconnect": "Disconnect", "configure": "Configure"}, "chats": {"title": "Chats", "newChat": "New Chat", "searchChats": "Search chats...", "typeMessage": "Type a message...", "send": "Send", "online": "Online", "offline": "Offline"}, "settings": {"title": "Settings", "description": "Manage your account settings and preferences."}, "buttons": {"save": "Save", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "create": "Create", "update": "Update", "submit": "Submit", "reset": "Reset", "back": "Back", "next": "Next", "previous": "Previous", "confirm": "Confirm", "close": "Close", "search": "Search", "filter": "Filter", "sort": "Sort", "export": "Export", "import": "Import", "download": "Download", "upload": "Upload"}, "common": {"loading": "Loading...", "noResults": "No results.", "error": "Error", "success": "Success", "warning": "Warning", "info": "Info", "yes": "Yes", "no": "No", "ok": "OK", "total": "Total", "selected": "Selected", "all": "All", "none": "None", "more": "More", "less": "Less", "show": "Show", "hide": "<PERSON>de", "expand": "Expand", "collapse": "Collapse", "welcome": "Welcome"}, "forms": {"required": "This field is required", "invalidEmail": "Invalid email address", "passwordTooShort": "Password must be at least 7 characters long", "passwordsNotMatch": "Passwords do not match", "pleaseEnterEmail": "Please enter your email", "pleaseEnterPassword": "Please enter your password", "title": "Title", "description": "Description", "name": "Name", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password"}, "messages": {"sessionExpired": "Session expired!", "internalServerError": "Internal Server Error!", "contentNotModified": "Content not modified!", "operationSuccessful": "Operation successful!", "operationFailed": "Operation failed!", "dataSubmitted": "You submitted the following values:"}, "language": {"chinese": "中文", "english": "English", "switchLanguage": "Switch Language"}, "marketing": {"features": "Features", "pricing": "Pricing", "blog": "Blog", "about": "About", "contact": "Contact", "dashboard": "Dashboard", "getStarted": "Get Started", "documentation": "Documentation", "changelog": "Changelog", "careers": "Careers", "helpCenter": "Help Center", "community": "Community", "support": "Support", "status": "Status", "privacy": "Privacy Policy", "terms": "Terms of Service", "security": "Security", "cookies": "<PERSON><PERSON>", "product": "Product", "company": "Company", "resources": "Resources", "legal": "Legal", "footerDescription": "Build beautiful admin dashboards with modern React components.", "allRightsReserved": "All rights reserved.", "builtWith": "Built with", "usingReact": "and React", "heroTitle": "Build Modern Admin Dashboards", "heroSubtitle": "Create beautiful, responsive admin interfaces quickly with Shadcn UI components. Fully open source and customizable.", "viewDemo": "View Demo", "learnMore": "Learn More"}}