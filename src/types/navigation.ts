import { ComponentType, ReactNode } from 'react'

// 基础导航链接类型
export interface BaseNavLink {
  title: string
  href: string
  isActive?: boolean
  disabled?: boolean
}

// TopNav 专用的链接类型
export interface TopNavLink extends BaseNavLink {
  // TopNav 特有的属性可以在这里扩展
}

// Breadcrumb 专用的链接类型
export interface BreadcrumbLink {
  title: string
  href?: string
  isLast?: boolean
}

// TabNav 专用的链接类型
export interface TabNavLink extends BaseNavLink {
  icon?: ReactNode
  badge?: string | number
}

// 导航组件类型枚举
export enum NavigationComponentType {
  NONE = 'none',
  TOP_NAV = 'topNav',
  BREADCRUMB = 'breadcrumb',
  TAB_NAV = 'tabNav',
  CUSTOM = 'custom',
}

// 导航组件的 Props 类型
export interface TopNavProps {
  links: TopNavLink[]
}

export interface BreadcrumbProps {
  links: BreadcrumbLink[]
  separator?: ReactNode
}

export interface TabNavProps {
  links: TabNavLink[]
  variant?: 'default' | 'pills' | 'underline'
}

// 自定义导航组件的配置
export interface CustomNavigationConfig {
  component: ComponentType<any>
  props: Record<string, any>
}

// 导航配置的联合类型
export type NavigationConfig =
  | { type: NavigationComponentType.NONE }
  | { type: NavigationComponentType.TOP_NAV; props: TopNavProps }
  | { type: NavigationComponentType.BREADCRUMB; props: BreadcrumbProps }
  | { type: NavigationComponentType.TAB_NAV; props: TabNavProps }
  | { type: NavigationComponentType.CUSTOM; config: CustomNavigationConfig }

// Header 配置接口
export interface HeaderConfig {
  fixed: boolean
  navigation: NavigationConfig
}

// 导航组件渲染器的类型
export type NavigationRenderer = (config: NavigationConfig) => ReactNode
