import { useAuthStore } from '@/stores/authStore'

export interface LoginCredentials {
  email: string
  password: string
}

export interface LoginResponse {
  user: {
    id: string
    accountNo: string
    email: string
    firstName: string
    lastName: string
    username: string
    avatar?: string
    role: string[]
    exp: number
    createdAt?: string
    updatedAt?: string
  }
  accessToken: string
  refreshToken?: string
}

export interface ApiError {
  message: string
  code?: string
  status?: number
}

/**
 * 模拟登录 API 调用
 * 在实际项目中，这里应该调用真实的 API
 */
export async function loginApi(credentials: LoginCredentials): Promise<LoginResponse> {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 1500))
  
  // 模拟登录验证
  if (credentials.email === '<EMAIL>' && credentials.password === 'password123') {
    return {
      user: {
        id: '1',
        accountNo: 'ACC001',
        email: credentials.email,
        firstName: 'Admin',
        lastName: 'User',
        username: 'admin',
        avatar: '/avatars/01.png',
        role: ['admin', 'superadmin'],
        exp: Date.now() + 24 * 60 * 60 * 1000, // 24小时后过期
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      accessToken: 'mock_jwt_token_' + Date.now(),
      refreshToken: 'mock_refresh_token_' + Date.now(),
    }
  } else if (credentials.email === '<EMAIL>' && credentials.password === 'password123') {
    return {
      user: {
        id: '2',
        accountNo: 'ACC002',
        email: credentials.email,
        firstName: 'John',
        lastName: 'Doe',
        username: 'johndoe',
        avatar: '/avatars/02.png',
        role: ['user'],
        exp: Date.now() + 24 * 60 * 60 * 1000,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      accessToken: 'mock_jwt_token_' + Date.now(),
      refreshToken: 'mock_refresh_token_' + Date.now(),
    }
  } else {
    throw new Error('Invalid email or password')
  }
}

/**
 * 模拟获取用户信息 API
 */
export async function getUserInfoApi(token: string): Promise<LoginResponse['user']> {
  await new Promise(resolve => setTimeout(resolve, 500))
  
  // 在实际项目中，这里会验证 token 并返回用户信息
  if (token.startsWith('mock_jwt_token_')) {
    return {
      id: '1',
      accountNo: 'ACC001',
      email: '<EMAIL>',
      firstName: 'Admin',
      lastName: 'User',
      username: 'admin',
      avatar: '/avatars/01.png',
      role: ['admin', 'superadmin'],
      exp: Date.now() + 24 * 60 * 60 * 1000,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }
  }
  
  throw new Error('Invalid token')
}

/**
 * 模拟刷新 token API
 */
export async function refreshTokenApi(refreshToken: string): Promise<{ accessToken: string }> {
  await new Promise(resolve => setTimeout(resolve, 500))
  
  if (refreshToken.startsWith('mock_refresh_token_')) {
    return {
      accessToken: 'mock_jwt_token_' + Date.now(),
    }
  }
  
  throw new Error('Invalid refresh token')
}

/**
 * 模拟退出登录 API
 */
export async function logoutApi(token: string): Promise<void> {
  await new Promise(resolve => setTimeout(resolve, 300))
  // 在实际项目中，这里会使 token 失效
  console.log('Logout API called with token:', token)
}

/**
 * 检查 token 是否过期
 */
export function isTokenExpired(exp: number): boolean {
  return Date.now() >= exp
}

/**
 * 获取认证头
 */
export function getAuthHeaders(token?: string): Record<string, string> {
  const authToken = token || useAuthStore.getState().accessToken
  
  if (!authToken) {
    return {}
  }
  
  return {
    'Authorization': `Bearer ${authToken}`,
    'Content-Type': 'application/json',
  }
}

/**
 * 检查用户是否有特定权限
 */
export function hasPermission(requiredRoles: string[]): boolean {
  const { user } = useAuthStore.getState()
  
  if (!user || !user.role) {
    return false
  }
  
  return requiredRoles.some(role => user.role.includes(role))
}

/**
 * 检查用户是否为管理员
 */
export function isAdmin(): boolean {
  return hasPermission(['admin', 'superadmin'])
}

/**
 * 检查用户是否为超级管理员
 */
export function isSuperAdmin(): boolean {
  return hasPermission(['superadmin'])
}
