import React, { StrictMode, Suspense, useEffect, useState } from 'react'
import ReactDOM from 'react-dom/client'
import { AxiosError } from 'axios'
import {
  QueryCache,
  QueryClient,
  QueryClientProvider,
} from '@tanstack/react-query'
import { RouterProvider, createRouter } from '@tanstack/react-router'
import { toast } from 'sonner'
import { useAuthStore } from '@/stores/authStore'
import { handleServerError } from '@/utils/handle-server-error'
import { FontProvider } from './context/font-context'
import { ThemeProvider } from './context/theme-context'
import { initializeAPI } from '@/lib/api'
import './lib/i18n'
import './index.css'
// Generated Routes
import { routeTree } from './routeTree.gen'

// 创建增强的 QueryClient，与 API 解决方案兼容
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: (failureCount, error) => {
        // eslint-disable-next-line no-console
        if (import.meta.env.DEV) console.log({ failureCount, error })

        // 开发环境下不重试，生产环境最多重试3次
        if (failureCount >= 0 && import.meta.env.DEV) return false
        if (failureCount > 3 && import.meta.env.PROD) return false

        // 不重试认证和权限错误
        return !(
          error instanceof AxiosError &&
          [401, 403].includes(error.response?.status ?? 0)
        )
      },
      refetchOnWindowFocus: import.meta.env.PROD,
      refetchOnReconnect: true,
      staleTime: 5 * 60 * 1000, // 5分钟，与 API 解决方案保持一致
      gcTime: 10 * 60 * 1000, // 10分钟（React Query v5 中 cacheTime 改名为 gcTime）
    },
    mutations: {
      retry: 1, // Mutation 重试1次
      onError: (error) => {
        // 保持现有的错误处理逻辑
        handleServerError(error)

        if (error instanceof AxiosError) {
          if (error.response?.status === 304) {
            toast.error('Content not modified!')
          }
        }
      },
    },
  },
  queryCache: new QueryCache({
    onError: (error) => {
      if (error instanceof AxiosError) {
        if (error.response?.status === 401) {
          toast.error('Session expired!')
          // 修复认证存储调用
          useAuthStore.getState().logout()
          const redirect = `${router.history.location.href}`
          router.navigate({ to: '/sign-in', search: { redirect } })
        }
        if (error.response?.status === 500) {
          toast.error('Internal Server Error!')
          router.navigate({ to: '/500' })
        }
        if (error.response?.status === 403) {
          // router.navigate("/forbidden", { replace: true });
        }
      }
    },
  }),
})

// Create a new router instance
const router = createRouter({
  routeTree,
  context: { queryClient },
  defaultPreload: 'intent',
  defaultPreloadStaleTime: 0,
})

// Register the router instance for type safety
declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router
  }
}

// API 初始化组件
function AppInitializer({ children }: { children: React.ReactNode }) {
  const [isAPIInitialized, setIsAPIInitialized] = useState(false)
  const [initError, setInitError] = useState<string | null>(null)

  useEffect(() => {
    const initAPI = async () => {
      try {
        await initializeAPI()
        setIsAPIInitialized(true)
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('Failed to initialize API:', error)
        setInitError(error instanceof Error ? error.message : 'Unknown error')
        // 即使 API 初始化失败，也允许应用继续运行
        setIsAPIInitialized(true)
      }
    }

    initAPI()
  }, [])

  if (!isAPIInitialized) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">正在初始化应用...</p>
        </div>
      </div>
    )
  }

  if (initError) {
    // eslint-disable-next-line no-console
    console.warn('API initialization failed, but app will continue:', initError)
  }

  return <>{children}</>
}

// 错误边界组件
class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // eslint-disable-next-line no-console
    console.error('Application error:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center max-w-md mx-auto p-6">
            <h1 className="text-2xl font-bold text-destructive mb-4">应用出现错误</h1>
            <p className="text-muted-foreground mb-4">
              应用遇到了一个意外错误。请刷新页面重试。
            </p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
            >
              刷新页面
            </button>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

// Render the app
const rootElement = document.getElementById('root')!
if (!rootElement.innerHTML) {
  const root = ReactDOM.createRoot(rootElement)
  root.render(
    <StrictMode>
      <ErrorBoundary>
        <AppInitializer>
          <QueryClientProvider client={queryClient}>
            <ThemeProvider defaultTheme='light' storageKey='vite-ui-theme'>
              <FontProvider>
                <Suspense fallback={
                  <div className="flex items-center justify-center min-h-screen">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                }>
                  <RouterProvider router={router} />
                </Suspense>
              </FontProvider>
            </ThemeProvider>
          </QueryClientProvider>
        </AppInitializer>
      </ErrorBoundary>
    </StrictMode>
  )
}
