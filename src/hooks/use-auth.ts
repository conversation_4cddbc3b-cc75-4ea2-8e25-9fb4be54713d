import { useCallback, useEffect } from 'react'
import { useNavigate } from '@tanstack/react-router'
import { toast } from 'sonner'
import { useAuthStore } from '@/stores/authStore'
import {
  loginApi,
  logoutApi,
  getUserInfoApi,
  isTokenExpired,
  type LoginCredentials
} from '@/services/auth'
import AuthStorage from '@/utils/auth-storage'

export function useAuth() {
  const authStore = useAuthStore()
  const navigate = useNavigate()

  // 登录函数
  const login = useCallback(async (credentials: LoginCredentials, redirectTo?: string) => {
    try {
      authStore.setLoading(true)

      const response = await login<PERSON>pi(credentials)

      // 设置用户信息和 token
      authStore.login(response.user, response.accessToken, response.refreshToken)

      toast.success('登录成功！', {
        description: `欢迎回来，${response.user.firstName || response.user.username}！`
      })

      // 重定向到指定页面或默认页面
      const targetPath = redirectTo || '/dashboard/overview'
      navigate({ to: targetPath })

      return response
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '登录失败'
      toast.error('登录失败', {
        description: errorMessage
      })
      throw error
    } finally {
      authStore.setLoading(false)
    }
  }, [authStore, navigate])

  // 退出登录函数
  const logout = useCallback(async (redirectTo?: string) => {
    try {
      const { accessToken } = authStore

      // 调用退出 API
      if (accessToken) {
        await logoutApi(accessToken)
      }

      // 清除本地状态
      authStore.logout()

      toast.success('已退出登录')

      // 重定向到指定页面或首页
      const targetPath = redirectTo || '/'
      navigate({ to: targetPath })

    } catch (error) {
      // 即使 API 调用失败，也要清除本地状态
      authStore.logout()

      const errorMessage = error instanceof Error ? error.message : '退出登录时发生错误'
      toast.error('退出登录失败', {
        description: errorMessage
      })

      // 仍然重定向
      const targetPath = redirectTo || '/'
      navigate({ to: targetPath })
    }
  }, [authStore, navigate])

  // 刷新用户信息
  const refreshUserInfo = useCallback(async () => {
    try {
      const { accessToken } = authStore

      if (!accessToken) {
        throw new Error('No access token available')
      }

      const userInfo = await getUserInfoApi(accessToken)
      authStore.setUser(userInfo)

      return userInfo
    } catch (error) {
      // console.error('Failed to refresh user info:', error)
      // 如果刷新失败，可能 token 已过期，执行退出登录
      await logout()
      throw error
    }
  }, [authStore, logout])

  // 检查认证状态
  const checkAuth = useCallback(async () => {
    const { user, accessToken, isAuthenticated } = authStore

    if (!isAuthenticated || !accessToken || !user) {
      return false
    }

    // 检查 token 是否过期
    if (isTokenExpired(user.exp)) {
      toast.error('登录已过期', {
        description: '请重新登录'
      })
      await logout()
      return false
    }

    return true
  }, [authStore, logout])

  // 自动检查认证状态（页面加载时）
  useEffect(() => {
    const initAuth = async () => {
      const { isAuthenticated, accessToken } = authStore

      if (isAuthenticated && accessToken) {
        try {
          await checkAuth()
        } catch {
          // console.error('Auth check failed:', error)
        }
      }
    }

    initAuth()
  }, [authStore, checkAuth]) // 依赖 authStore 和 checkAuth 函数

  // 定期检查 token 是否过期
  useEffect(() => {
    const { isAuthenticated } = authStore

    if (!isAuthenticated) {
      return
    }

    const interval = setInterval(async () => {
      await checkAuth()
    }, 5 * 60 * 1000) // 每5分钟检查一次

    return () => clearInterval(interval)
  }, [authStore, checkAuth])

  // 权限检查函数
  const hasPermission = useCallback((requiredRoles: string[]) => {
    return AuthStorage.hasPermission(requiredRoles)
  }, [])

  const isAdmin = useCallback(() => {
    return AuthStorage.isAdmin()
  }, [])

  const isSuperAdmin = useCallback(() => {
    return AuthStorage.isSuperAdmin()
  }, [])

  // 更新最后活动时间
  const updateLastActivity = useCallback(() => {
    authStore.updateLastActivity()
  }, [authStore])

  return {
    // 状态
    user: authStore.user,
    accessToken: authStore.accessToken,
    isAuthenticated: authStore.isAuthenticated,
    isLoading: authStore.isLoading,

    // 计算属性
    userDisplayName: authStore.getUserDisplayName(),
    userInitials: authStore.getUserInitials(),

    // 方法
    login,
    logout,
    refreshUserInfo,
    checkAuth,
    updateLastActivity,

    // 权限检查
    hasPermission,
    isAdmin,
    isSuperAdmin,

    // Store 方法的直接访问
    setLoading: authStore.setLoading,
    setUser: authStore.setUser,
    setAccessToken: authStore.setAccessToken,

    // 存储工具方法
    shouldRefreshToken: authStore.shouldRefreshToken,
    checkTokenExpiry: authStore.checkTokenExpiry,
  }
}
